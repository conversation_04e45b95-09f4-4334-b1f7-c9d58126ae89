import fs from "fs";
import path from "path";
import { NextRequest, NextResponse } from "next/server";

/**
 * GET /api/daycare-properties-2023
 * 获取2023年托儿所属性数据（用于与ZIP边界合并）
 *
 * 查询参数:
 * - zip: 特定的ZIP code (可选)
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const zipCode = searchParams.get("zip");

    console.log(
      "Daycare properties 2023 request for:",
      zipCode || "all ZIP codes"
    );

    // 读取属性数据文件
    const filePath = path.join(
      process.cwd(),
      "data/processed_2023/daycare_properties_2023.json"
    );

    if (!fs.existsSync(filePath)) {
      return NextResponse.json(
        {
          success: false,
          error: "Daycare properties data file not found",
          note: 'Run "node data/create_2023_data.js" to generate the data',
          timestamp: new Date().toISOString(),
        },
        { status: 404 }
      );
    }

    const fileContents = fs.readFileSync(filePath, "utf8");
    const data = JSON.parse(fileContents);

    // 如果请求特定的ZIP code
    if (zipCode) {
      const zipData = data.properties[zipCode];

      if (!zipData) {
        return NextResponse.json(
          {
            success: false,
            error: `ZIP code ${zipCode} not found in 2023 properties data`,
            timestamp: new Date().toISOString(),
          },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        data: zipData,
        metadata: data.metadata,
        timestamp: new Date().toISOString(),
      });
    }

    // 返回所有数据
    return NextResponse.json({
      success: true,
      data: data.properties,
      metadata: data.metadata,
      count: Object.keys(data.properties).length,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error fetching daycare properties 2023:", error);

    return NextResponse.json(
      {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to fetch daycare properties 2023",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
