{"name": "mapbox", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@heroicons/react": "^2.2.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/line-clamp": "^0.4.4", "@types/nprogress": "^0.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.5", "i18next": "^25.3.1", "i18next-browser-languagedetector": "^8.2.0", "immer": "^10.1.1", "lucide-react": "^0.525.0", "mapbox-gl": "^3.13.0", "next": "15.3.5", "next-nprogress-bar": "^2.4.7", "next-themes": "^0.4.6", "nprogress": "^0.2.0", "postcss-pxtorem": "^6.1.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "react-i18next": "^15.6.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/postcss-pxtorem": "^6.1.0", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "critters": "^0.0.23", "eslint": "^9", "eslint-config-next": "15.3.5", "postcss": "^8.4.49", "tailwindcss": "^3.4.17", "typescript": "^5"}}