"use client";

import { ReactNode } from "react";
import { useComponentPreload, ROUTE_GROUPS } from "@/hooks/useComponentPreload";

interface PreloadWrapperProps {
  children: ReactNode;
  routes?: readonly string[];
  delay?: number;
}

export const PreloadWrapper = ({
  children,
  routes = ROUTE_GROUPS.ALL,
  delay = 2000,
}: PreloadWrapperProps) => {
  // 预加载其他路由
  useComponentPreload({
    routes,
    delay, // 页面加载完成后开始预加载
  });

  return <>{children}</>;
};
