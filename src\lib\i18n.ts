import i18n from "i18next";
import LanguageDetector from "i18next-browser-languagedetector";
import { initReactI18next } from "react-i18next";
import { LANGUAGE_CONFIG } from "@/config";
import { I18N_NAMESPACES } from "@/constants";
import enUS from "@/locales/en-US";
import zhCN from "@/locales/zh-CN";

export enum SUPPORTED_LANGUAGES {
  "zh-CN" = "zh-CN",
  "en-US" = "en-US",
}
const resources = {
  "zh-CN": {
    ...zhCN,
  },
  "en-US": {
    ...enUS,
  },
};

// 初始化 i18n
const initI18n = async (storeLanguage?: string) => {
  try {
    // 如果提供了 store 中的语言，优先使用它
    let initialLanguage: string;

    if (
      storeLanguage &&
      Object.values(SUPPORTED_LANGUAGES).includes(
        storeLanguage as SUPPORTED_LANGUAGES
      )
    ) {
      initialLanguage = storeLanguage;
    } else {
      initialLanguage = LANGUAGE_CONFIG.FALLBACK_INITIAL_LANGUAGE;
    }

    // 语言检测配置
    const detectionConfig = {
      order: ["localStorage", "navigator", "htmlTag"],
      lookupLocalStorage: LANGUAGE_CONFIG.STORAGE_KEY,
      caches: ["localStorage"],
    };

    await i18n
      // 检测用户语言
      .use(LanguageDetector)
      // 传递 i18n 实例给 react-i18next
      .use(initReactI18next)
      // 初始化 i18next
      .init({
        resources,
        fallbackLng: SUPPORTED_LANGUAGES["zh-CN"],
        debug: process.env.NODE_ENV === "development",

        // 语言检测配置
        detection: detectionConfig,

        // 设置初始语言
        lng: initialLanguage,

        interpolation: {
          escapeValue: false, // React 已经默认转义了
        },

        // 命名空间
        defaultNS: "common",
        ns: I18N_NAMESPACES,
      });
  } catch (error) {
    console.error("Failed to initialize i18n:", error);
  }
};

// 导出初始化函数，允许外部传入 store 语言
export const initializeI18nWithStore = async (storeLanguage?: string) => {
  if (!i18n.isInitialized) {
    await initI18n(storeLanguage);
  } else if (storeLanguage && i18n.language !== storeLanguage) {
    // 如果已经初始化但语言不同，切换语言
    await i18n.changeLanguage(storeLanguage);
  }
};

// 默认初始化（不使用 store 语言）
if (!i18n.isInitialized) {
  initI18n();
}

export default i18n;
