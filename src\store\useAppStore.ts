import React from "react";
import { create } from "zustand";
import { persist } from "zustand/middleware";

interface AppState {
  // 路由状态
  currentRoute: string;
  previousRoute: string;

  // UI 状态
  sidebarCollapsed: boolean;

  // 性能状态
  routeLoadTimes: Record<string, number>;

  // Actions
  setCurrentRoute: (route: string) => void;
  toggleSidebar: () => void;
  setSidebarCollapsed: (collapsed: boolean) => void;
  recordRouteLoadTime: (route: string, time: number) => void;
  headerSlot: React.ReactNode | null;
  setHeaderSlot: (slot: React.ReactNode) => void;
}

export const useAppStore = create<AppState>()(
  persist(
    (set, get) => ({
      // 初始状态
      currentRoute: "/",
      previousRoute: "/",
      sidebarCollapsed: false,
      routeLoadTimes: {},
      headerSlot: null,

      // Actions
      setCurrentRoute: (route: string) => {
        const { currentRoute } = get();
        set({
          previousRoute: currentRoute,
          currentRoute: route,
        });
      },

      toggleSidebar: () => {
        set((state) => ({
          sidebarCollapsed: !state.sidebarCollapsed,
        }));
      },

      setSidebarCollapsed: (collapsed: boolean) => {
        set({ sidebarCollapsed: collapsed });
      },

      recordRouteLoadTime: (route: string, time: number) => {
        set((state) => ({
          routeLoadTimes: {
            ...state.routeLoadTimes,
            [route]: time,
          },
        }));
      },
      setHeaderSlot: (slot: React.ReactNode) => {
        set({ headerSlot: slot });
      },
    }),
    {
      name: "app-storage",
      // 只持久化需要的状态
      partialize: (state) => ({
        sidebarCollapsed: state.sidebarCollapsed,
        routeLoadTimes: state.routeLoadTimes,
      }),
    }
  )
);
