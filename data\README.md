# 加州托儿所饱和度数据处理方案

## 项目概述

基于原始 CSV 数据 `zip_code_saturation.csv` 实现加州托儿所市场饱和度地图可视化应用。本项目专注于展示**2023 年**的托儿所市场饱和度数据。

## 文件说明

### 原始数据文件

- `zip_code_saturation.csv` - 包含 2021-2023 年加州托儿所饱和度原始数据

### 数据处理脚本

- `create_2023_data.js` - 专门处理 2023 年数据的脚本

### 处理后数据目录

- `processed_2023/` - 包含所有处理后的 2023 年数据文件

## 原始数据结构

```csv
zip_code,year,birth_count,facility_capacity,saturation_rate,saturation_percentage
90001,2021,732.0,685.0,0.9357923497267759,93.5792349726776
90001,2022,711.0,685.0,0.9634317862165963,96.34317862165963
90001,2023,676.0,685.0,1.0133136094674555,101.33136094674555
```

### 数据特点

- **时间跨度**: 2021-2023 年，每个 ZIP 码有 3 年数据
- **处理策略**: 仅使用 2023 年数据进行地图展示
- **字段说明**:
  - `zip_code`: 加州邮政编码
  - `year`: 年份
  - `birth_count`: 出生数量
  - `facility_capacity`: 托儿所容量
  - `saturation_rate`: 饱和度 (capacity/births)
  - `saturation_percentage`: 饱和度百分比

### 数据处理策略

- 过滤出 2023 年数据
- 清理无效记录（birth_count=0, facility_capacity=0）
- 生成加州范围内的地理坐标
- 计算饱和度等级分类

## 数据处理步骤

### 运行 2023 年数据处理脚本

```bash
node data/create_2023_data.js
```

**处理逻辑**：

1. 读取原始 CSV 文件
2. 过滤出 2023 年数据
3. 清理无效记录
4. 生成加州范围内的地理坐标
5. 计算饱和度等级分类
6. 输出多种格式的 2023 年数据

### 生成的 2023 年数据文件

`processed_2023/` 目录包含以下文件：

- `daycare_data_2023.geojson` - Mapbox 可直接使用的 GeoJSON 格式
- `api_data_2023.json` - API 接口简化数据
- `processed_data_2023.json` - 完整的处理数据
- `stats_2023.json` - 2023 年统计信息

### 2023 年数据统计

处理完成后的数据概览：

- **总 ZIP 码数量**: 1,269 个
- **饱和度范围**: 0.11 - 49.21
- **平均饱和度**: 3.26
- **总出生数**: 395,152
- **总容量**: 1,034,262

### 第二步：饱和度等级分类

```javascript
function getSaturationLevel(saturation) {
  if (saturation <= 0.5) return "low"; // 🟢 低饱和度
  if (saturation <= 1.0) return "medium"; // 🟡 中等饱和度
  if (saturation <= 2.0) return "high"; // 🟠 高饱和度
  return "very-high"; // 🔴 极高饱和度
}
```

### 第三步：坐标生成

由于原始数据缺少坐标，使用算法生成：

```javascript
// 基于ZIP码生成固定的伪随机坐标
function generateCACoordinates(zipCode) {
  const CA_BOUNDS = {
    north: 42.0,
    south: 32.5,
    east: -114.1,
    west: -124.4,
  };

  const seed = parseInt(zipCode) || 90210;
  const random1 = ((seed * 9301 + 49297) % 233280) / 233280;
  const random2 = (((seed + 1) * 9301 + 49297) % 233280) / 233280;

  const lat = CA_BOUNDS.south + (CA_BOUNDS.north - CA_BOUNDS.south) * random1;
  const lng = CA_BOUNDS.west + (CA_BOUNDS.east - CA_BOUNDS.west) * random2;

  return [lng, lat];
}
```

## Mapbox 集成方案

### 1. 2023 年 GeoJSON 数据格式

```json
{
  "type": "FeatureCollection",
  "metadata": {
    "title": "California Daycare Saturation 2023",
    "description": "2023年加州托儿所市场饱和度数据",
    "year": 2023,
    "total_features": 1269
  },
  "features": [
    {
      "type": "Feature",
      "geometry": {
        "type": "Point",
        "coordinates": [-122.766, 33.628]
      },
      "properties": {
        "zip_code": "94027",
        "total_births": 24,
        "total_capacity": 1181,
        "saturation": 49.21,
        "saturation_level": "very-high",
        "saturation_level_text": "极高饱和度",
        "year": 2023
      }
    }
  ]
}
```

### 2. 2023 年地图配置

```javascript
// Mapbox地图初始化
const map = new mapboxgl.Map({
  container: "map",
  style: "mapbox://styles/mapbox/light-v11",
  center: [-119.4179, 36.7783], // 加州中心
  zoom: 6,
});

// 添加2023年数据源
map.addSource("daycare-data-2023", {
  type: "geojson",
  data: "/data/processed_2023/daycare_data_2023.geojson", // 使用2023年数据
});
```

### 3. 圆点样式配置

```javascript
// 添加圆点图层
map.addLayer({
  id: "daycare-circles",
  type: "circle",
  source: "daycare-data",
  paint: {
    "circle-radius": [
      "interpolate",
      ["linear"],
      ["get", "saturation"],
      0,
      5, // 最小半径
      5,
      20, // 最大半径
    ],
    "circle-color": [
      "case",
      ["<=", ["get", "saturation"], 0.5],
      "#22c55e", // 绿色-低饱和度
      ["<=", ["get", "saturation"], 1.0],
      "#eab308", // 黄色-中等饱和度
      ["<=", ["get", "saturation"], 2.0],
      "#f97316", // 橙色-高饱和度
      "#ef4444", // 红色-极高饱和度
    ],
    "circle-opacity": 0.8,
    "circle-stroke-width": 1,
    "circle-stroke-color": "#ffffff",
  },
});
```

### 4. 点击事件和弹窗

```javascript
// 点击事件
map.on("click", "daycare-circles", (e) => {
  const properties = e.features[0].properties;

  const popup = new mapboxgl.Popup()
    .setLngLat(e.lngLat)
    .setHTML(
      `
      <div class="popup-content">
        <h3>ZIP Code: ${properties.zip_code}</h3>
        <p><strong>总容量:</strong> ${properties.total_capacity}</p>
        <p><strong>总出生数:</strong> ${properties.total_births}</p>
        <p><strong>饱和度:</strong> ${properties.saturation}</p>
        <p><strong>等级:</strong> ${properties.saturation_level}</p>
      </div>
    `
    )
    .addTo(map);
});
```

### 5. 搜索功能

```javascript
// 使用Mapbox Geocoding API
async function searchAddress(query) {
  const response = await fetch(
    `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(
      query
    )}.json?` +
      `access_token=${mapboxgl.accessToken}&` +
      `country=US&` +
      `bbox=-124.4,32.5,-114.1,42.0` // 限制在加州范围
  );

  const data = await response.json();
  return data.features;
}
```

## 2023 年 API 接口设计

### 1. 获取 2023 年所有数据

```
GET /api/daycare-data-2023
返回: 所有ZIP码的2023年饱和度数据
```

### 2. 获取特定 ZIP 码的 2023 年数据

```
GET /api/daycare-data-2023?zip=94027
返回: 指定ZIP码的2023年详细数据
```

### 3. 获取 2023 年 GeoJSON 数据

```
GET /api/daycare-data-2023?format=geojson
返回: Mapbox可用的2023年GeoJSON格式数据
```

### 4. 获取 2023 年统计信息

```
GET /api/daycare-data-2023/stats
返回: 2023年数据集统计信息
```

## 下一步实现

1. **运行 2023 年数据处理**: `node data/create_2023_data.js` ✅ 已完成
2. **创建 2023 年 API 路由**: 实现上述 2023 年专用 API 接口
3. **实现地图组件**: 使用 Mapbox GL JS 展示 2023 年数据
4. **添加搜索功能**: 集成 Geocoding API
5. **优化用户体验**: 添加加载状态、错误处理等

## 当前文件结构

```
data/
├── zip_code_saturation.csv         # 原始数据 (2021-2023年)
├── create_2023_data.js             # 2023年数据处理脚本 ✅
├── README.md                       # 本文档
└── processed_2023/                 # 2023年处理后的数据 ✅
    ├── daycare_data_2023.geojson   # 2023年GeoJSON格式
    ├── api_data_2023.json          # 2023年API数据
    ├── processed_data_2023.json    # 2023年完整数据
    └── stats_2023.json             # 2023年统计信息
```

## 2023 年数据优势

- **数据一致性**: 所有数据来自同一年份，无时间差异
- **最新数据**: 2023 年是最新的完整年度数据
- **性能优化**: 数据量减少约 2/3，加载更快
- **简化分析**: 专注于地理分布，无需考虑年度变化
- **用户体验**: 界面更简洁，信息更聚焦

## 使用说明

1. 确保已安装 Node.js
2. 运行数据处理脚本: `node data/create_2023_data.js`
3. 使用生成的文件构建地图应用
4. 参考本文档的 Mapbox 集成方案进行开发
