"use client";

import { useRouter } from "next/navigation";
import { useEffect } from "react";

interface PreloadConfig {
  routes: readonly string[] | string[];
  delay?: number;
}

export const useComponentPreload = ({
  routes,
  delay = 1000,
}: PreloadConfig) => {
  const router = useRouter();

  useEffect(() => {
    const timer = setTimeout(() => {
      routes.forEach((route) => {
        router.prefetch(route);
      });
    }, delay);

    return () => clearTimeout(timer);
  }, [routes, delay, router]);
};

// 预定义的路由组
export const ROUTE_GROUPS = {
  ALL: ["/"],
} as const;
