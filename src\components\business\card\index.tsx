import clsx from "clsx";
import { Card as CardPrimitive, CardContent } from "@/components/ui/card";

export function Card({
  children,
  className,
  classContentName,
  ...props
}: {
  children: React.ReactNode;
  className?: string;
  classContentName?: string;
}) {
  return (
    <CardPrimitive
      {...props}
      className={clsx("w-full  border-border py-4", className)}
    >
      <CardContent className={classContentName}>{children}</CardContent>
    </CardPrimitive>
  );
}
