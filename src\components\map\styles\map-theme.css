/**
 * 地图主题样式
 * 为地图组件提供主题感知的样式
 */

/* Mapbox GL 控件主题适配 */
[data-theme="light"] .mapboxgl-ctrl-group {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  border-radius: var(--radius) !important;
  box-shadow: var(--shadow-md) !important;
}

[data-theme="dark"] .mapboxgl-ctrl-group {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  border-radius: var(--radius) !important;
  box-shadow: var(--shadow-md) !important;
}

/* 导航控件按钮 */
[data-theme="light"] .mapboxgl-ctrl-group button {
  background-color: transparent !important;
  color: var(--text-primary) !important;
  border: none !important;
}

[data-theme="dark"] .mapboxgl-ctrl-group button {
  background-color: transparent !important;
  color: var(--text-primary) !important;
  border: none !important;
}

[data-theme="light"] .mapboxgl-ctrl-group button:hover {
  background-color: var(--surface-light) !important;
}

[data-theme="dark"] .mapboxgl-ctrl-group button:hover {
  background-color: var(--surface-light) !important;
}

/* 比例尺控件 */
[data-theme="light"] .mapboxgl-ctrl-scale {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
  border-radius: var(--radius) !important;
  font-family: var(--font-geist-sans) !important;
}

[data-theme="dark"] .mapboxgl-ctrl-scale {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
  border-radius: var(--radius) !important;
  font-family: var(--font-geist-sans) !important;
}

/* 地图容器 */
.map-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: var(--radius);
  border: 1px solid var(--border);
  background-color: var(--surface);
}

/* 地图加载状态 */
.map-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--surface);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.map-loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border);
  border-top: 3px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 地图错误状态 */
.map-error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 1rem;
  box-shadow: var(--shadow-lg);
  max-width: 300px;
  text-align: center;
}

.map-error-icon {
  color: var(--destructive);
  margin-bottom: 0.5rem;
}

.map-error-title {
  color: var(--text-primary);
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.map-error-description {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* 地图控件容器 */
.map-controls {
  position: absolute;
  top: 1rem;
  right: 1rem;
  z-index: 10;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .map-controls {
    top: 0.5rem;
    right: 0.5rem;
  }

  .mapboxgl-ctrl-group {
    margin: 0 !important;
  }

  .mapboxgl-ctrl-group button {
    width: 32px !important;
    height: 32px !important;
  }
}

/* 地图弹窗 - 无动画，直接显示 */
.mapboxgl-popup {
  /* 移除动画效果，直接显示 */
}

/* 地图图层切换动画 */
.map-layer-transition {
  transition: opacity 0.3s ease-in-out;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  [data-theme="light"] .mapboxgl-ctrl-group {
    border: 2px solid var(--border) !important;
  }

  [data-theme="dark"] .mapboxgl-ctrl-group {
    border: 2px solid var(--border) !important;
  }

  .map-container {
    border: 2px solid var(--border);
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .map-loading-spinner {
    animation: none;
  }

  .mapboxgl-popup {
    animation: none;
  }

  .map-layer-transition {
    transition: none;
  }
}

/* 打印样式 */
@media print {
  .map-controls {
    display: none !important;
  }

  .mapboxgl-ctrl-group {
    display: none !important;
  }

  .map-container {
    border: 1px solid #000 !important;
  }
}
