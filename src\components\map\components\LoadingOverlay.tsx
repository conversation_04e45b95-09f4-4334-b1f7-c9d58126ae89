"use client";

import React from "react";
import { useMapLanguage } from "@/hooks/useMapLanguage";

interface LoadingOverlayProps {
  isLoading: boolean;
  loadingStatus: string;
  className?: string;
}

/**
 * 地图加载状态覆盖层组件
 * 显示地图数据加载进度和状态
 */
export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  isLoading,
  loadingStatus,
  className = "",
}) => {
  const { t } = useMapLanguage();

  if (!isLoading) {
    return null;
  }

  return (
    <div
      className={`absolute inset-0 flex items-center justify-center z-20 ${className}`}
      style={{
        background: "var(--surface)",
        opacity: 0.95,
      }}
    >
      <div className="text-center">
        <div
          className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 mb-4"
          style={{ borderColor: "var(--primary)" }}
        ></div>
        <div className="font-medium" style={{ color: "var(--text-primary)" }}>
          {loadingStatus}
        </div>
        <div
          className="text-sm mt-2"
          style={{ color: "var(--text-secondary)" }}
        >
          {t("map:loading.pleaseWait")}
        </div>
      </div>
    </div>
  );
};
