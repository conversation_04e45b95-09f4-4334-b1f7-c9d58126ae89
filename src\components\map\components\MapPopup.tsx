"use client";

import React, { useEffect } from "react";
import { useMapLanguage } from "@/hooks/useMapLanguage";
import type { DaycareFeature } from "@/types/daycare";

interface MapPopupProps {
  popup: any; // Mapbox GL Popup instance
  feature: DaycareFeature;
}

/**
 * 地图弹窗组件
 * 负责显示托儿所数据的详细信息
 */
export const MapPopup: React.FC<MapPopupProps> = ({ popup, feature }) => {
  const { t } = useMapLanguage();

  useEffect(() => {
    if (popup && feature) {
      const htmlContent = createPopupHTML(feature.properties, t);
      popup.setHTML(htmlContent);

      // 强制应用主题样式
      setTimeout(() => {
        applyThemeToPopup(popup);
      }, 50);
    }
  }, [popup, feature, t]);

  return null; // 这个组件不渲染任何内容，只是管理弹窗的HTML
};

/**
 * 强制应用主题样式到弹窗
 */
const applyThemeToPopup = (popup: any) => {
  if (typeof window === "undefined") {
    return;
  }

  const currentTheme =
    (document.documentElement.getAttribute("data-theme") as "light" | "dark") ||
    "dark";
  const popupElement = popup.getElement();

  if (!popupElement) {
    return;
  }

  const popupContent = popupElement.querySelector(".mapboxgl-popup-content");
  if (!popupContent) {
    return;
  }

  // 应用主题样式
  const themeStyles = {
    light: {
      backgroundColor: "#f8f9fa",
      color: "#111827",
      border: "1px solid #e5e7eb",
      borderRadius: "0.625rem",
      boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1)",
    },
    dark: {
      backgroundColor: "#111111",
      color: "#ffffff",
      border: "1px solid #2a2a2a",
      borderRadius: "0.625rem",
      boxShadow: "0 10px 40px rgba(0, 0, 0, 0.6)",
    },
  };

  const styles = themeStyles[currentTheme];
  Object.assign((popupContent as HTMLElement).style, styles);

  // 应用到内部元素
  const popupHeader = popupContent.querySelector(".popup-header");
  if (popupHeader) {
    const headerElement = popupHeader as HTMLElement;
    headerElement.style.background =
      currentTheme === "light"
        ? "linear-gradient(135deg, #ff2e4d 0%, #1d4ed8 100%)"
        : "linear-gradient(135deg, #ff2e4d 0%, #1e40af 100%)";
    headerElement.style.color = "#ffffff";
  }

  const popupContentDiv = popupContent.querySelector(".popup-content");
  if (popupContentDiv) {
    const contentElement = popupContentDiv as HTMLElement;
    contentElement.style.backgroundColor = styles.backgroundColor;
    contentElement.style.color = styles.color;
  }

  // 应用到统计项目
  const statItems = popupContent.querySelectorAll(".stat-item");
  statItems.forEach((item: any) => {
    const element = item as HTMLElement;
    element.style.backgroundColor =
      currentTheme === "light" ? "#ffffff" : "#1a1a1a";
    element.style.borderColor =
      currentTheme === "light" ? "#e5e7eb" : "#2a2a2a";
  });

  // 应用到饱和度指示器
  const saturationIndicator = popupContent.querySelector(
    ".saturation-indicator"
  );
  if (saturationIndicator) {
    const element = saturationIndicator as HTMLElement;
    element.style.backgroundColor =
      currentTheme === "light" ? "#ffffff" : "#1a1a1a";
  }
};

/**
 * 获取饱和度级别的翻译文本
 */
const getSaturationLevelText = (
  level: string,
  t: (key: string) => string
): string => {
  switch (level) {
    case "low":
      return t("map:legend.lowSaturation");
    case "medium":
      return t("map:legend.mediumSaturation");
    case "high":
      return t("map:legend.highSaturation");
    case "very-high":
      return t("map:legend.veryHighSaturation");
    default:
      return level;
  }
};

/**
 * 创建弹窗HTML内容
 */
const createPopupHTML = (
  props: DaycareFeature["properties"],
  t: (key: string) => string
): string => {
  const saturationLevel = props.saturation_level.replace("-", "-");
  const saturationLevelText = getSaturationLevelText(props.saturation_level, t);

  return `
    <div class="daycare-popup">
      <div class="popup-header">
        <div class="popup-close" onclick="this.closest('.mapboxgl-popup').remove()"></div>
        <h3>${t("map:popup.title")}</h3>
        <div class="zip-badge">${t("map:popup.zipCode")} ${props.zip_code}</div>
      </div>

      <div class="popup-content">
        <div class="popup-stats">
          <div class="stat-item">
            <div class="stat-value">${props.total_capacity.toLocaleString()}</div>
            <div class="stat-label">${t("map:popup.totalCapacity")}</div>
          </div>

          <div class="stat-item">
            <div class="stat-value">${props.total_births.toLocaleString()}</div>
            <div class="stat-label">${t("map:popup.totalBirths")}</div>
          </div>
        </div>

        <div class="saturation-indicator ${saturationLevel}">
          <div>
            <div class="saturation-text">${saturationLevelText}</div>
            <div class="saturation-label">${t(
              "map:popup.saturationLevel"
            )}</div>
          </div>
          <div class="saturation-value">${props.saturation.toFixed(2)}</div>
        </div>
      </div>
    </div>
  `;
};

/**
 * 弹窗样式组件
 * 提供全局样式，应该在应用的根级别使用
 * 支持主题适配
 */
export const MapPopupStyles: React.FC = () => {
  return (
    <style jsx global>{`
      /* Mapbox弹窗基础样式 */
      .mapboxgl-popup {
        max-width: 320px !important;
        font-family: var(--font-geist-sans), -apple-system, BlinkMacSystemFont,
          "Segoe UI", Roboto, sans-serif !important;
      }

      /* 浅色主题弹窗样式 - 提高优先级 */
      html[data-theme="light"] .mapboxgl-popup-content {
        padding: 0 !important;
        border-radius: var(--radius) !important;
        box-shadow: var(--shadow-lg) !important;
        border: 1px solid var(--border) !important;
        background: var(--surface) !important;
        color: var(--text-primary) !important;
        overflow: hidden !important;
        backdrop-filter: blur(10px) !important;
        transform: none !important;
        animation: none !important;
        transition: none !important;
      }

      /* 深色主题弹窗样式 - 提高优先级 */
      html[data-theme="dark"] .mapboxgl-popup-content {
        padding: 0 !important;
        border-radius: var(--radius) !important;
        box-shadow: var(--shadow-lg) !important;
        border: 1px solid var(--border) !important;
        background: var(--surface) !important;
        color: var(--text-primary) !important;
        overflow: hidden !important;
        backdrop-filter: blur(10px) !important;
        transform: none !important;
        animation: none !important;
        transition: none !important;
      }

      /* 默认样式（回退） */
      .mapboxgl-popup-content {
        padding: 0 !important;
        border-radius: 16px !important;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15),
          0 4px 12px rgba(0, 0, 0, 0.1) !important;
        border: 1px solid rgba(0, 0, 0, 0.05) !important;
        background: linear-gradient(
          135deg,
          #ffffff 0%,
          #f8fafc 100%
        ) !important;
        overflow: hidden !important;
        backdrop-filter: blur(10px) !important;
        transform: none !important;
        animation: none !important;
        transition: none !important;
      }

      .mapboxgl-popup-close-button {
        display: none !important;
      }

      .mapboxgl-popup-tip {
        border-top-color: white !important;
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1)) !important;
      }

      /* 弹窗内容样式 */
      .daycare-popup {
        background: transparent;
        border-radius: 16px;
        overflow: hidden;
      }

      /* 弹窗头部主题适配 - 提高优先级 */
      html[data-theme="light"] .popup-header {
        background: linear-gradient(
          135deg,
          var(--primary) 0%,
          #1d4ed8 100%
        ) !important;
        color: var(--primary-foreground) !important;
        padding: 16px 20px;
        position: relative;
        overflow: hidden;
      }

      html[data-theme="dark"] .popup-header {
        background: linear-gradient(
          135deg,
          var(--primary) 0%,
          #1e40af 100%
        ) !important;
        color: var(--primary-foreground) !important;
        padding: 16px 20px;
        position: relative;
        overflow: hidden;
      }

      .popup-header {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        color: white;
        padding: 16px 20px;
        position: relative;
        overflow: hidden;
      }

      .popup-header::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") !important;
        pointer-events: none;
      }

      /* 弹窗标题主题适配 */
      [data-theme="light"] .popup-header h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        position: relative;
        z-index: 1;
        color: var(--primary-foreground);
      }

      [data-theme="dark"] .popup-header h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        position: relative;
        z-index: 1;
        color: var(--primary-foreground);
      }

      .popup-header h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        position: relative;
        z-index: 1;
        color: white;
      }

      /* ZIP 徽章主题适配 */
      [data-theme="light"] .popup-header .zip-badge {
        display: inline-block;
        background: rgba(255, 255, 255, 0.2);
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
        margin-top: 4px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        color: var(--primary-foreground);
      }

      [data-theme="dark"] .popup-header .zip-badge {
        display: inline-block;
        background: rgba(255, 255, 255, 0.15);
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
        margin-top: 4px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        color: var(--primary-foreground);
      }

      .popup-header .zip-badge {
        display: inline-block;
        background: rgba(255, 255, 255, 0.2);
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
        margin-top: 4px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        color: white;
      }

      /* 弹窗内容区域 */
      [data-theme="light"] .popup-content {
        padding: 20px;
        background: var(--surface);
        color: var(--text-primary);
      }

      [data-theme="dark"] .popup-content {
        padding: 20px;
        background: var(--surface);
        color: var(--text-primary);
      }

      .popup-content {
        padding: 20px;
        background: white;
        color: #1e293b;
      }

      .popup-stats {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;
        margin-bottom: 16px;
      }

      /* 统计项目主题适配 */
      [data-theme="light"] .stat-item {
        text-align: center;
        padding: 12px;
        background: var(--surface-light);
        border-radius: var(--radius);
        border: 1px solid var(--border);
      }

      [data-theme="dark"] .stat-item {
        text-align: center;
        padding: 12px;
        background: var(--surface-light);
        border-radius: var(--radius);
        border: 1px solid var(--border);
      }

      .stat-item {
        text-align: center;
        padding: 12px;
        background: #f8fafc;
        border-radius: 12px;
        border: 1px solid #e2e8f0;
      }

      /* 统计值主题适配 */
      [data-theme="light"] .stat-value {
        font-size: 20px;
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: 4px;
      }

      [data-theme="dark"] .stat-value {
        font-size: 20px;
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: 4px;
      }

      .stat-value {
        font-size: 20px;
        font-weight: 700;
        color: #1e293b;
        margin-bottom: 4px;
      }

      /* 统计标签主题适配 */
      [data-theme="light"] .stat-label {
        font-size: 12px;
        color: var(--text-secondary);
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      [data-theme="dark"] .stat-label {
        font-size: 12px;
        color: var(--text-secondary);
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .stat-label {
        font-size: 12px;
        color: #64748b;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      /* 饱和度标签主题适配 */
      [data-theme="light"] .saturation-label {
        font-size: 12px;
        color: var(--text-secondary);
        margin-top: 2px;
      }

      [data-theme="dark"] .saturation-label {
        font-size: 12px;
        color: var(--text-secondary);
        margin-top: 2px;
      }

      .saturation-label {
        font-size: 12px;
        color: #64748b;
        margin-top: 2px;
      }

      /* 饱和度指示器基础样式 */
      [data-theme="light"] .saturation-indicator {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        background: var(--surface-light);
        border-radius: var(--radius);
        border-left: 4px solid;
        margin-top: 12px;
      }

      [data-theme="dark"] .saturation-indicator {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        background: var(--surface-light);
        border-radius: var(--radius);
        border-left: 4px solid;
        margin-top: 12px;
      }

      .saturation-indicator {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        background: #f8fafc;
        border-radius: 12px;
        border-left: 4px solid;
        margin-top: 12px;
      }

      /* 饱和度级别颜色 - 浅色主题 */
      [data-theme="light"] .saturation-indicator.low {
        border-left-color: #22c55e;
        background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
      }

      [data-theme="light"] .saturation-indicator.medium {
        border-left-color: #eab308;
        background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);
      }

      [data-theme="light"] .saturation-indicator.high {
        border-left-color: #f97316;
        background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);
      }

      [data-theme="light"] .saturation-indicator.very-high {
        border-left-color: #ef4444;
        background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
      }

      /* 饱和度级别颜色 - 深色主题 */
      [data-theme="dark"] .saturation-indicator.low {
        border-left-color: #34d399;
        background: linear-gradient(
          135deg,
          rgba(52, 211, 153, 0.1) 0%,
          rgba(52, 211, 153, 0.05) 100%
        );
      }

      [data-theme="dark"] .saturation-indicator.medium {
        border-left-color: #fbbf24;
        background: linear-gradient(
          135deg,
          rgba(251, 191, 36, 0.1) 0%,
          rgba(251, 191, 36, 0.05) 100%
        );
      }

      [data-theme="dark"] .saturation-indicator.high {
        border-left-color: #fb923c;
        background: linear-gradient(
          135deg,
          rgba(251, 146, 60, 0.1) 0%,
          rgba(251, 146, 60, 0.05) 100%
        );
      }

      [data-theme="dark"] .saturation-indicator.very-high {
        border-left-color: #f87171;
        background: linear-gradient(
          135deg,
          rgba(248, 113, 113, 0.1) 0%,
          rgba(248, 113, 113, 0.05) 100%
        );
      }

      /* 默认饱和度级别颜色（回退） */
      .saturation-indicator.low {
        border-left-color: #22c55e;
        background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
      }

      .saturation-indicator.medium {
        border-left-color: #eab308;
        background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);
      }

      .saturation-indicator.high {
        border-left-color: #f97316;
        background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);
      }

      .saturation-indicator.very-high {
        border-left-color: #ef4444;
        background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
      }

      /* 饱和度文本主题适配 */
      [data-theme="light"] .saturation-text {
        font-weight: 600;
        font-size: 14px;
        color: var(--text-primary);
      }

      [data-theme="dark"] .saturation-text {
        font-weight: 600;
        font-size: 14px;
        color: var(--text-primary);
      }

      .saturation-text {
        font-weight: 600;
        font-size: 14px;
        color: #1e293b;
      }

      /* 饱和度值主题适配 */
      [data-theme="light"] .saturation-value {
        font-size: 18px;
        font-weight: 700;
        color: var(--text-primary);
      }

      [data-theme="dark"] .saturation-value {
        font-size: 18px;
        font-weight: 700;
        color: var(--text-primary);
      }

      .saturation-value {
        font-size: 18px;
        font-weight: 700;
        color: #1e293b;
      }

      /* 关闭按钮主题适配 */
      [data-theme="light"] .popup-close {
        position: absolute;
        top: 12px;
        right: 12px;
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        backdrop-filter: blur(10px);
        z-index: 2;
      }

      [data-theme="dark"] .popup-close {
        position: absolute;
        top: 12px;
        right: 12px;
        background: rgba(255, 255, 255, 0.15);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        backdrop-filter: blur(10px);
        z-index: 2;
      }

      .popup-close {
        position: absolute;
        top: 12px;
        right: 12px;
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        backdrop-filter: blur(10px);
        z-index: 2;
      }

      /* 关闭按钮图标 */
      [data-theme="light"] .popup-close::before,
      [data-theme="light"] .popup-close::after {
        content: "";
        position: absolute;
        width: 12px;
        height: 2px;
        background: var(--primary-foreground);
        border-radius: 1px;
      }

      [data-theme="dark"] .popup-close::before,
      [data-theme="dark"] .popup-close::after {
        content: "";
        position: absolute;
        width: 12px;
        height: 2px;
        background: var(--primary-foreground);
        border-radius: 1px;
      }

      .popup-close::before,
      .popup-close::after {
        content: "";
        position: absolute;
        width: 12px;
        height: 2px;
        background: white;
        border-radius: 1px;
      }

      .popup-close::before {
        transform: rotate(45deg);
      }

      .popup-close::after {
        transform: rotate(-45deg);
      }

      /* 移除动画效果，直接显示 */
      .mapboxgl-popup {
        /* 不使用动画，直接显示 */
      }

      /* 响应式设计 */
      @media (max-width: 480px) {
        .mapboxgl-popup {
          max-width: 280px !important;
        }

        .popup-header {
          padding: 14px 16px;
        }

        .popup-content {
          padding: 16px;
        }

        .popup-stats {
          grid-template-columns: 1fr;
          gap: 12px;
        }
      }
    `}</style>
  );
};
