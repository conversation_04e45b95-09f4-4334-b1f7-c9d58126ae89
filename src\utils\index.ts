/**
 * 工具函数库
 * 统一导出所有工具函数，提供便捷的访问接口
 */

import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

// 重新导出所有工具函数模块
export * from "./responsive";

/**
 * 合并 CSS 类名的工具函数
 * 支持 Tailwind CSS 类名冲突解决
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * 防抖函数
 */
export function debounce<T extends (...args: unknown[]) => unknown>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * 节流函数
 */
export function throttle<T extends (...args: unknown[]) => unknown>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

// 注意：大部分工具函数已按功能分类到对应的模块文件中
