import fs from "fs";
import path from "path";
import { NextResponse } from "next/server";
import type { DaycareStats, ApiResponse } from "@/types/daycare";

/**
 * GET /api/daycare-data-2023/stats
 * 获取2023年托儿所数据统计信息
 */
export async function GET() {
  try {
    const statsPath = path.join(
      process.cwd(),
      "data/processed_2023/stats_2023.json"
    );

    // 检查文件是否存在
    if (!fs.existsSync(statsPath)) {
      return NextResponse.json(
        {
          success: false,
          error:
            "Stats file not found. Please run data processing script first.",
          timestamp: new Date().toISOString(),
        } as ApiResponse<null>,
        { status: 404 }
      );
    }

    // 读取统计数据
    const statsContent = fs.readFileSync(statsPath, "utf8");
    const stats = JSON.parse(statsContent) as DaycareStats;

    // 验证数据结构
    if (!isValidStatsData(stats)) {
      return NextResponse.json(
        {
          success: false,
          error: "Invalid stats data format",
          timestamp: new Date().toISOString(),
        } as ApiResponse<null>,
        { status: 500 }
      );
    }

    // 添加额外的计算统计
    const enhancedStats = {
      ...stats,
      computed: {
        // 计算市场机会指标
        market_opportunity_percentage: Math.round(
          (stats.level_distribution.low / stats.total_zip_codes) * 100
        ),
        // 计算高竞争区域百分比
        high_competition_percentage: Math.round(
          ((stats.level_distribution.high +
            stats.level_distribution.very_high) /
            stats.total_zip_codes) *
            100
        ),
        // 计算平均容量利用率
        average_capacity_utilization: Math.round(
          (stats.births.total / stats.capacity.total) * 100
        ),
        // 计算数据完整性
        data_completeness: {
          total_records: stats.total_zip_codes,
          valid_records: stats.total_zip_codes,
          completeness_percentage: 100,
        },
      },
      // 添加趋势分析（基于饱和度分布）
      trends: {
        market_saturation_trend: getMarketTrend(stats.level_distribution),
        recommended_action: getRecommendedAction(stats.level_distribution),
        growth_potential_areas:
          stats.level_distribution.low + stats.level_distribution.medium,
      },
    };

    return NextResponse.json({
      success: true,
      data: enhancedStats,
      timestamp: new Date().toISOString(),
    } as ApiResponse<typeof enhancedStats>);
  } catch (error) {
    console.error("Error loading 2023 stats:", error);

    return NextResponse.json(
      {
        success: false,
        error: "Failed to load 2023 statistics",
        timestamp: new Date().toISOString(),
      } as ApiResponse<null>,
      { status: 500 }
    );
  }
}

/**
 * 验证统计数据格式
 */
function isValidStatsData(stats: any): stats is DaycareStats {
  return (
    stats &&
    typeof stats.year === "number" &&
    typeof stats.total_zip_codes === "number" &&
    stats.saturation &&
    typeof stats.saturation.min === "number" &&
    typeof stats.saturation.max === "number" &&
    typeof stats.saturation.avg === "number" &&
    stats.level_distribution &&
    typeof stats.level_distribution.low === "number" &&
    typeof stats.level_distribution.medium === "number" &&
    typeof stats.level_distribution.high === "number" &&
    typeof stats.level_distribution.very_high === "number"
  );
}

/**
 * 分析市场趋势
 */
function getMarketTrend(
  distribution: DaycareStats["level_distribution"]
): string {
  const total =
    distribution.low +
    distribution.medium +
    distribution.high +
    distribution.very_high;
  const highSaturationPercentage =
    ((distribution.high + distribution.very_high) / total) * 100;

  if (highSaturationPercentage > 70) {
    return "oversaturated";
  } else if (highSaturationPercentage > 50) {
    return "highly_competitive";
  } else if (highSaturationPercentage > 30) {
    return "moderately_competitive";
  } else {
    return "growth_opportunity";
  }
}

/**
 * 获取推荐行动
 */
function getRecommendedAction(
  distribution: DaycareStats["level_distribution"]
): string {
  const total =
    distribution.low +
    distribution.medium +
    distribution.high +
    distribution.very_high;
  const lowSaturationPercentage = (distribution.low / total) * 100;
  const highSaturationPercentage =
    ((distribution.high + distribution.very_high) / total) * 100;

  if (lowSaturationPercentage > 20) {
    return "expand_in_low_saturation_areas";
  } else if (highSaturationPercentage > 60) {
    return "focus_on_service_differentiation";
  } else {
    return "balanced_expansion_strategy";
  }
}

/**
 * OPTIONS /api/daycare-data-2023/stats
 * CORS预检请求
 */
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type",
    },
  });
}
