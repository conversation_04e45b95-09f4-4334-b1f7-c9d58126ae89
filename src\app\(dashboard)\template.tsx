"use client";

import { motion } from "framer-motion";
import { RouteErrorBoundary } from "@/components/RouteErrorBoundary";
export default function Template({ children }: { children: React.ReactNode }) {
  return (
    <RouteErrorBoundary>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.15, ease: "easeOut" }}
        className="h-full"
      >
        {children}
      </motion.div>
    </RouteErrorBoundary>
  );
}
