"use client";

import React, { useEffect, useState } from "react";
import { LocalDataLoader } from "@/components/map/data/local-data-manager";

export default function TestLocalDataPage() {
  const [status, setStatus] = useState<string>("初始化中...");
  const [dataInfo, setDataInfo] = useState<any>({});
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const testLocalData = async () => {
      try {
        setStatus("正在初始化本地数据管理器...");
        const loader = new LocalDataLoader();
        await loader.initialize();

        setStatus("正在加载数据...");
        
        // 测试各种数据加载
        const zipBoundaries = await loader.preloadZipBoundaries();
        const daycareData = await loader.loadDaycareData();
        const stats = await loader.getDaycareStats();
        const zipData = await loader.getZipData("90210");

        setDataInfo({
          zipBoundaries: {
            featuresCount: zipBoundaries?.features?.length || 0,
            type: zipBoundaries?.type || "unknown"
          },
          daycareData: {
            featuresCount: daycareData?.features?.length || 0,
            type: daycareData?.type || "unknown"
          },
          stats: {
            totalZipCodes: stats?.total_zip_codes || 0,
            year: stats?.year || "unknown",
            levelDistribution: stats?.level_distribution || {}
          },
          zipData: {
            hasData: !!zipData,
            keys: zipData ? Object.keys(zipData) : []
          }
        });

        setStatus("✅ 所有数据加载完成");
      } catch (err) {
        console.error("测试失败:", err);
        setError(err instanceof Error ? err.message : "未知错误");
        setStatus("❌ 数据加载失败");
      }
    };

    testLocalData();
  }, []);

  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900 py-8">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-bold text-center mb-8 text-gray-900 dark:text-white">
            本地数据管理器测试
          </h1>

          {/* 状态显示 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
              加载状态
            </h2>
            <div className="text-lg text-gray-700 dark:text-gray-300">
              {status}
            </div>
            {error && (
              <div className="mt-4 p-4 bg-red-100 dark:bg-red-900/20 border border-red-300 dark:border-red-700 rounded-lg">
                <div className="text-red-800 dark:text-red-200 font-semibold">错误:</div>
                <div className="text-red-600 dark:text-red-300">{error}</div>
              </div>
            )}
          </div>

          {/* 数据信息显示 */}
          {Object.keys(dataInfo).length > 0 && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* ZIP边界数据 */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                  ZIP边界数据
                </h3>
                <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                  <div>类型: {dataInfo.zipBoundaries?.type}</div>
                  <div>特征数量: {dataInfo.zipBoundaries?.featuresCount}</div>
                </div>
              </div>

              {/* 托儿所数据 */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                  托儿所数据
                </h3>
                <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                  <div>类型: {dataInfo.daycareData?.type}</div>
                  <div>特征数量: {dataInfo.daycareData?.featuresCount}</div>
                </div>
              </div>

              {/* 统计数据 */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                  统计数据
                </h3>
                <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                  <div>年份: {dataInfo.stats?.year}</div>
                  <div>ZIP区域总数: {dataInfo.stats?.totalZipCodes}</div>
                  <div>等级分布:</div>
                  <div className="ml-4">
                    <div>低: {dataInfo.stats?.levelDistribution?.low || 0}</div>
                    <div>中: {dataInfo.stats?.levelDistribution?.medium || 0}</div>
                    <div>高: {dataInfo.stats?.levelDistribution?.high || 0}</div>
                    <div>很高: {dataInfo.stats?.levelDistribution?.very_high || 0}</div>
                  </div>
                </div>
              </div>

              {/* ZIP特定数据 */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                  ZIP数据 (90210)
                </h3>
                <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                  <div>有数据: {dataInfo.zipData?.hasData ? "是" : "否"}</div>
                  <div>属性数量: {dataInfo.zipData?.keys?.length || 0}</div>
                  {dataInfo.zipData?.keys?.length > 0 && (
                    <div>
                      <div>属性: {dataInfo.zipData.keys.slice(0, 5).join(", ")}</div>
                      {dataInfo.zipData.keys.length > 5 && <div>...</div>}
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
