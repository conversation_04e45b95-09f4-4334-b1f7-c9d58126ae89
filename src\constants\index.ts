/**
 * 常量定义中心
 *
 * 注意：大部分配置已重新组织到 src/config/ 目录下
 * 建议使用: import { ... } from "@/config"
 */

// 国际化命名空间（保留在此处，因为与i18n配置紧密相关）
export const I18N_NAMESPACES = [
  "common",
  "theme",
  "language",
  "navigation",
  "pages",
  "components",
  "effects",
  "errors",
  "validation",
  "message",
  "nav",
  "route",
  "button",
  "modal",
  "home",
  "informationSource",
] as const;

// 注意：验证规则已移动到 src/config/config.ts
// 请使用: import { VALIDATION_RULES } from "@/config"

// 开发环境配置（保留在此处，因为是开发时使用的常量）
export const DEV_CONFIG = {
  enableDebug: process.env.NODE_ENV === "development",
  logLevel: process.env.NEXT_PUBLIC_LOG_LEVEL || "info",
} as const;
