"use client";

import { useEffect, useState, useMemo, useCallback, useRef } from "react";
import { useTranslation } from "react-i18next";
import { SUPPORTED_LANGUAGES } from "@/lib/i18n";
import useConfigStore from "@/store/useConfig";

// 回退翻译文本
const fallbackTranslations: Record<string, Record<string, string>> = {
  "zh-CN": {
    "map:search.placeholder": "搜索加州地址...",
    "map:search.clearSearch": "清空搜索",
    "map:loading.initializing": "初始化地图...",
    "map:loading.loadingBoundaries": "加载ZIP边界数据...",
    "map:loading.loadingData": "加载托儿所数据...",
    "map:loading.complete": "完成",
    "map:loading.rendering": "渲染中...",
    "map:loading.pleaseWait": "正在加载地图数据，请稍候...",
    "map:boundaries.show": "显示边界",
    "map:boundaries.hide": "隐藏边界",
    "map:common.loading": "加载中...",
    "map:popup.title": "托儿所数据分析",
    "map:popup.zipCode": "ZIP",
    "map:popup.totalCapacity": "总容量",
    "map:popup.totalBirths": "总出生数",
    "map:popup.saturationLevel": "饱和度等级",
    "map:errors.mapInitError": "地图初始化错误",
    "map:errors.searchFailed": "搜索请求失败",
    "map:legend.title": "2023年饱和度图例",
    "map:legend.totalZipCodes": "总ZIP码数量:",
    "map:legend.averageSaturation": "平均饱和度:",
    "map:legend.marketStatus": "市场状态:",
    "map:legend.circleSizeExplanation": "圆点大小说明:",
    "map:legend.lowSaturation": "低饱和度",
    "map:legend.mediumSaturation": "中等饱和度",
    "map:legend.highSaturation": "高饱和度",
    "map:legend.veryHighSaturation": "极高饱和度",
    "map:legend.dataSource": "数据来源: 2023年加州托儿所市场调研",
    "map:legend.marketOverSaturated": "市场过饱和",
    "map:legend.highCompetition": "高度竞争",
    "map:legend.moderateCompetition": "适度竞争",
    "map:legend.growthOpportunity": "增长机会",
    "map:legend.mostAreasCompetitive": "大部分地区竞争激烈",
    "map:legend.majorityAreasCompetitive": "多数地区竞争较强",
    "map:legend.moderateCompetitionLevel": "竞争程度适中",
    "map:legend.marketOpportunityExists": "存在市场机会",
    "map:page.title": "加州托儿所市场饱和度分析",
    "map:page.dataYear": "数据年份:",
    "map:map.loadingData": "加载地图数据...",
    "map:map.loadError": "地图加载错误",
    "map:error.mapLoadFailed": "地图加载失败",
    "map:error.mapLoadFailedDesc":
      "抱歉，地图组件遇到了问题。这可能是由于网络连接问题或配置错误导致的。",
    "map:error.errorDetails": "错误详情:",
    "map:error.reload": "重新加载",
    "map:error.refreshPage": "刷新页面",
    "map:error.possibleSolutions": "可能的解决方案:",
    "map:error.checkNetwork": "• 检查网络连接是否正常",
    "map:error.checkMapboxToken": "• 确认Mapbox访问令牌是否有效",
    "map:error.tryRefresh": "• 尝试刷新页面",
    "map:error.contactSupport": "• 如果问题持续存在，请联系技术支持",
    "map:error.retry": "重试",
    "map:error.componentError": "地图组件遇到了问题",
  },
  "en-US": {
    "map:search.placeholder": "Search California addresses...",
    "map:search.clearSearch": "Clear search",
    "map:loading.initializing": "Initializing map...",
    "map:loading.loadingBoundaries": "Loading ZIP boundaries...",
    "map:loading.loadingData": "Loading daycare data...",
    "map:loading.complete": "Complete",
    "map:loading.rendering": "Rendering...",
    "map:loading.pleaseWait": "Loading map data, please wait...",
    "map:boundaries.show": "Show Boundaries",
    "map:boundaries.hide": "Hide Boundaries",
    "map:common.loading": "Loading...",
    "map:popup.title": "Daycare Data Analysis",
    "map:popup.zipCode": "ZIP",
    "map:popup.totalCapacity": "Total Capacity",
    "map:popup.totalBirths": "Total Births",
    "map:popup.saturationLevel": "Saturation Level",
    "map:errors.mapInitError": "Map initialization error",
    "map:errors.searchFailed": "Search request failed",
    "map:legend.title": "2023 Saturation Legend",
    "map:legend.totalZipCodes": "Total ZIP Codes:",
    "map:legend.averageSaturation": "Average Saturation:",
    "map:legend.marketStatus": "Market Status:",
    "map:legend.circleSizeExplanation": "Circle Size Explanation:",
    "map:legend.lowSaturation": "Low Saturation",
    "map:legend.mediumSaturation": "Medium Saturation",
    "map:legend.highSaturation": "High Saturation",
    "map:legend.veryHighSaturation": "Very High Saturation",
    "map:legend.dataSource":
      "Data Source: 2023 California Daycare Market Research",
    "map:legend.marketOverSaturated": "Market Over-saturated",
    "map:legend.highCompetition": "High Competition",
    "map:legend.moderateCompetition": "Moderate Competition",
    "map:legend.growthOpportunity": "Growth Opportunity",
    "map:legend.mostAreasCompetitive": "Most areas highly competitive",
    "map:legend.majorityAreasCompetitive": "Majority areas competitive",
    "map:legend.moderateCompetitionLevel": "Moderate competition level",
    "map:legend.marketOpportunityExists": "Market opportunities exist",
    "map:page.title": "California Daycare Market Saturation Analysis",
    "map:page.dataYear": "Data Year:",
    "map:map.loadingData": "Loading map data...",
    "map:map.loadError": "Map loading error",
    "map:error.mapLoadFailed": "Map Loading Failed",
    "map:error.mapLoadFailedDesc":
      "Sorry, the map component encountered an issue. This may be due to network connection problems or configuration errors.",
    "map:error.errorDetails": "Error Details:",
    "map:error.reload": "Reload",
    "map:error.refreshPage": "Refresh Page",
    "map:error.possibleSolutions": "Possible Solutions:",
    "map:error.checkNetwork": "• Check if network connection is working",
    "map:error.checkMapboxToken": "• Verify Mapbox access token is valid",
    "map:error.tryRefresh": "• Try refreshing the page",
    "map:error.contactSupport":
      "• Contact technical support if problem persists",
    "map:error.retry": "Retry",
    "map:error.componentError": "Map component encountered an issue",
  },
};

/**
 * 安全的地图多语言Hook
 * 带有错误处理和回退机制
 */
export function useMapLanguage() {
  const [isI18nReady, setIsI18nReady] = useState(false);
  const [currentLang, setCurrentLang] = useState<SUPPORTED_LANGUAGES>(
    SUPPORTED_LANGUAGES["zh-CN"]
  );

  // 始终调用hooks（不能在条件语句中调用）
  const { t: i18nTranslation, ready: i18nReady } = useTranslation("map", {
    useSuspense: false,
  });
  const { language: storeLanguageValue } = useConfigStore();

  // 使用 ref 来稳定 i18nTranslation 的引用
  const i18nTranslationRef = useRef(i18nTranslation);
  i18nTranslationRef.current = i18nTranslation;

  // 使用 useMemo 来稳定 storeLanguage 的引用
  const storeLanguage = useMemo(() => {
    return (
      (storeLanguageValue as SUPPORTED_LANGUAGES) ||
      SUPPORTED_LANGUAGES["zh-CN"]
    );
  }, [storeLanguageValue]);

  // 更新当前语言
  useEffect(() => {
    setCurrentLang(storeLanguage);
    setIsI18nReady(i18nReady);
  }, [storeLanguage, i18nReady]);

  // 使用 useCallback 来稳定翻译函数的引用
  const t = useCallback(
    (key: string): string => {
      // 如果i18n准备好了，尝试使用它
      if (isI18nReady && i18nTranslationRef.current) {
        try {
          const result = i18nTranslationRef.current(key);
          if (result && result !== key) {
            return result;
          }
        } catch (error) {
          console.warn(`Translation failed for key: ${key}`, error);
        }
      }

      // 回退到硬编码翻译
      const fallback = fallbackTranslations[currentLang]?.[key];
      if (fallback) {
        return fallback;
      }

      // 最后回退到中文
      const chineseFallback = fallbackTranslations["zh-CN"]?.[key];
      if (chineseFallback) {
        return chineseFallback;
      }

      // 如果都没有，返回key本身
      console.warn(`No translation found for key: ${key}`);
      return key;
    },
    [isI18nReady, currentLang]
  );

  return {
    t,
    currentLanguage: currentLang,
    isReady: isI18nReady,
  };
}
