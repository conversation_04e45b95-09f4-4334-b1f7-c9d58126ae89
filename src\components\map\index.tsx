import React from "react";
import { useDaycareMap } from "@/hooks/useDaycareMap";
import { useMapLanguage } from "@/hooks/useMapLanguage";
import MapContainer from "./MapContainer";
import MapErrorBoundary from "./MapErrorBoundary";
import MapLegend from "./MapLegend";
import SearchBox from "./SearchBox";

/**
 * 主要的地图组件 - 整合所有地图相关功能
 */
const DaycareMap: React.FC = () => {
  const { t } = useMapLanguage();
  const {
    mapState,
    searchQuery,
    searchResults,
    handleSearch,
    handleSearchResultSelect,
    handleMapLoad,
    handleMapError,
  } = useDaycareMap();

  return (
    <MapErrorBoundary>
      <div className="relative w-full h-full">
        {/* 搜索框 */}
        <div className="absolute top-4 left-4 z-10">
          <SearchBox
            value={searchQuery}
            results={searchResults}
            onSearch={handleSearch}
            onResultSelect={handleSearchResultSelect}
            className="w-80"
          />
        </div>

        {/* 地图容器 */}
        <MapContainer
          className="w-full h-full"
          onMapLoad={handleMapLoad}
          onError={handleMapError}
        />

        {/* 图例 */}
        {mapState.stats && (
          <div className="absolute bottom-4 left-4 z-10">
            <MapLegend stats={mapState.stats} />
          </div>
        )}

        {/* 加载状态 */}
        {/* {mapState.isLoading && (
          <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center z-20">
            <div className="bg-white rounded-lg p-6 flex items-center space-x-3">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              <span className="text-gray-700">{t("map:map.loadingData")}</span>
            </div>
          </div>
        )} */}

        {/* 错误状态 */}
        {mapState.error && (
          <div className="absolute top-20 left-4 right-4 z-20">
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg
                    className="h-5 w-5 text-red-400"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">
                    {t("map:map.loadError")}
                  </h3>
                  <div className="mt-2 text-sm text-red-700">
                    <p>{mapState.error}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 数据信息面板 */}
        {/* {mapState.stats && (
          <div className="absolute top-2 right-12 z-10">
            <div className="bg-white rounded-lg shadow-lg p-4 max-w-xs">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                2023年数据概览
              </h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">总ZIP码:</span>
                  <span className="font-medium">
                    {mapState.stats.total_zip_codes}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">平均饱和度:</span>
                  <span className="font-medium">
                    {mapState.stats.saturation.avg.toFixed(2)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">总出生数:</span>
                  <span className="font-medium">
                    {mapState.stats.births.total.toLocaleString()}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">总容量:</span>
                  <span className="font-medium">
                    {mapState.stats.capacity.total.toLocaleString()}
                  </span>
                </div>
              </div>
            </div>
          </div>
        )} */}
      </div>
    </MapErrorBoundary>
  );
};

export default DaycareMap;
