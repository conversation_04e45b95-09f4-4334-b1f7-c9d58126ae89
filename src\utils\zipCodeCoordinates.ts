/**
 * ZIP Code坐标计算工具
 * 根据ZIP code边界数据计算区域中心点
 */

// 计算多边形的质心（中心点）
export function calculatePolygonCentroid(
  coordinates: number[][][]
): [number, number] {
  // 取第一个多边形环（外环）
  const ring = coordinates[0];

  let area = 0;
  let centroidX = 0;
  let centroidY = 0;

  // 使用Shoelace公式计算面积和质心
  for (let i = 0; i < ring.length - 1; i++) {
    const [x1, y1] = ring[i];
    const [x2, y2] = ring[i + 1];

    const crossProduct = x1 * y2 - x2 * y1;
    area += crossProduct;
    centroidX += (x1 + x2) * crossProduct;
    centroidY += (y1 + y2) * crossProduct;
  }

  area = area / 2;
  centroidX = centroidX / (6 * area);
  centroidY = centroidY / (6 * area);

  return [centroidX, centroidY];
}

// 计算边界框的中心点（备用方法）
export function calculateBoundingBoxCenter(
  coordinates: number[][][]
): [number, number] {
  const ring = coordinates[0];

  let minX = Infinity;
  let maxX = -Infinity;
  let minY = Infinity;
  let maxY = -Infinity;

  ring.forEach(([x, y]) => {
    minX = Math.min(minX, x);
    maxX = Math.max(maxX, x);
    minY = Math.min(minY, y);
    maxY = Math.max(maxY, y);
  });

  return [(minX + maxX) / 2, (minY + maxY) / 2];
}

// 从ZIP边界数据中提取坐标映射
export function extractZipCoordinates(
  zipBoundariesData: any
): Map<string, [number, number]> {
  const zipCoordinates = new Map<string, [number, number]>();

  if (!zipBoundariesData?.features) {
    console.warn("No ZIP boundaries features found");
    return zipCoordinates;
  }

  zipBoundariesData.features.forEach((feature: any) => {
    try {
      // 获取ZIP code
      const zipCode =
        feature.properties?.ZCTA5CE10 ||
        feature.properties?.zipCode ||
        feature.properties?.zip_code ||
        feature.properties?.ZIP;

      if (!zipCode) {
        console.warn(
          "No ZIP code found in feature properties:",
          feature.properties
        );
        return;
      }

      // 获取几何数据
      const geometry = feature.geometry;
      if (!geometry || geometry.type !== "Polygon") {
        console.warn(`Invalid geometry for ZIP ${zipCode}:`, geometry?.type);
        return;
      }

      // 计算中心点
      let center: [number, number];
      try {
        center = calculatePolygonCentroid(geometry.coordinates);

        // 验证坐标是否有效
        if (!isFinite(center[0]) || !isFinite(center[1])) {
          console.warn(
            `Invalid centroid for ZIP ${zipCode}, using bounding box center`
          );
          center = calculateBoundingBoxCenter(geometry.coordinates);
        }
      } catch (error) {
        console.warn(
          `Error calculating centroid for ZIP ${zipCode}, using bounding box center:`,
          error
        );
        center = calculateBoundingBoxCenter(geometry.coordinates);
      }

      zipCoordinates.set(zipCode, center);
    } catch (error) {
      console.error("Error processing ZIP boundary feature:", error);
    }
  });

  console.log(`Extracted coordinates for ${zipCoordinates.size} ZIP codes`);
  return zipCoordinates;
}

// 获取特定ZIP code的坐标
export function getZipCoordinate(
  zipCode: string,
  zipCoordinates: Map<string, [number, number]>
): [number, number] | null {
  return zipCoordinates.get(zipCode) || null;
}

// 验证坐标是否在加州范围内
export function isCoordinateInCalifornia(
  coordinate: [number, number]
): boolean {
  const [lng, lat] = coordinate;

  // 加州大致边界
  const CA_BOUNDS = {
    west: -124.4,
    east: -114.1,
    south: 32.5,
    north: 42.0,
  };

  return (
    lng >= CA_BOUNDS.west &&
    lng <= CA_BOUNDS.east &&
    lat >= CA_BOUNDS.south &&
    lat <= CA_BOUNDS.north
  );
}

// 批量更新托儿所数据的坐标
export function updateDaycareCoordinates(
  daycareData: any[],
  zipCoordinates: Map<string, [number, number]>
): any[] {
  let updatedCount = 0;
  let notFoundCount = 0;

  const updatedData = daycareData.map((item) => {
    const zipCode = item.zip_code;
    const newCoordinate = getZipCoordinate(zipCode, zipCoordinates);

    if (newCoordinate && isCoordinateInCalifornia(newCoordinate)) {
      updatedCount++;
      return {
        ...item,
        longitude: newCoordinate[0],
        latitude: newCoordinate[1],
        coordinate_source: "zip_boundary_centroid",
      };
    } else {
      notFoundCount++;
      console.warn(`No valid coordinate found for ZIP ${zipCode}`);
      return {
        ...item,
        coordinate_source: "original",
      };
    }
  });

  console.log(
    `Updated coordinates: ${updatedCount} success, ${notFoundCount} not found`
  );
  return updatedData;
}

// 批量更新GeoJSON格式的托儿所数据坐标
export function updateDaycareGeoJSONCoordinates(
  geoJsonData: any,
  zipCoordinates: Map<string, [number, number]>
): any {
  let updatedCount = 0;
  let notFoundCount = 0;

  const updatedFeatures = geoJsonData.features.map((feature: any) => {
    const zipCode = feature.properties?.zip_code;
    const newCoordinate = getZipCoordinate(zipCode, zipCoordinates);

    if (newCoordinate && isCoordinateInCalifornia(newCoordinate)) {
      updatedCount++;
      return {
        ...feature,
        geometry: {
          ...feature.geometry,
          coordinates: newCoordinate,
        },
        properties: {
          ...feature.properties,
          coordinate_source: "zip_boundary_centroid",
        },
      };
    } else {
      notFoundCount++;
      if (zipCode) {
        console.warn(`No valid coordinate found for ZIP ${zipCode}`);
      }
      return {
        ...feature,
        properties: {
          ...feature.properties,
          coordinate_source: "original",
        },
      };
    }
  });

  console.log(
    `Updated GeoJSON coordinates: ${updatedCount} success, ${notFoundCount} not found`
  );

  return {
    ...geoJsonData,
    features: updatedFeatures,
    metadata: {
      ...geoJsonData.metadata,
      coordinate_update: {
        updated_count: updatedCount,
        not_found_count: notFoundCount,
        updated_at: new Date().toISOString(),
      },
    },
  };
}

// 合并托儿所数据和ZIP边界数据，创建带有饱和度信息的ZIP区域数据
export function mergeZipBoundariesWithDaycareData(
  zipBoundariesData: any,
  daycareData: any
): any {
  console.log("🔄 开始合并ZIP边界和托儿所数据...");
  console.log("🔄 输入数据检查:", {
    zipBoundariesData: !!zipBoundariesData,
    zipFeatures: zipBoundariesData?.features?.length || 0,
    daycareData: !!daycareData,
    daycareFeatures: daycareData?.features?.length || 0,
  });

  if (!zipBoundariesData?.features || !daycareData?.features) {
    console.error(
      "❌ Invalid data provided to mergeZipBoundariesWithDaycareData"
    );
    return null;
  }

  // 创建托儿所数据的ZIP码映射
  const daycareByZip = new Map<string, any>();
  daycareData.features.forEach((feature: any, index: number) => {
    const zipCode = feature.properties?.zip_code;
    if (zipCode) {
      daycareByZip.set(zipCode, feature.properties);
      if (index < 5) {
        console.log(`🔄 托儿所数据示例 ${index + 1}:`, {
          zip: zipCode,
          saturation: feature.properties?.saturation,
          level: feature.properties?.saturation_level,
        });
      }
    }
  });

  console.log(`🔄 托儿所数据包含 ${daycareByZip.size} 个ZIP码`);

  // 检查ZIP边界数据的属性结构
  const firstZipFeature = zipBoundariesData.features[0];
  console.log("🔄 ZIP边界数据示例属性:", {
    ZCTA5CE10: firstZipFeature?.properties?.ZCTA5CE10,
    zipCode: firstZipFeature?.properties?.zipCode,
    zip_code: firstZipFeature?.properties?.zip_code,
    ZIP: firstZipFeature?.properties?.ZIP,
    allProps: Object.keys(firstZipFeature?.properties || {}),
  });

  // 合并ZIP边界和托儿所数据
  const mergedFeatures = zipBoundariesData.features
    .map((zipFeature: any) => {
      const zipCode =
        zipFeature.properties?.ZCTA5CE10 ||
        zipFeature.properties?.zipCode ||
        zipFeature.properties?.zip_code ||
        zipFeature.properties?.ZIP;

      if (!zipCode) {
        return null;
      }

      const daycareInfo = daycareByZip.get(zipCode);

      if (!daycareInfo) {
        // 如果没有托儿所数据，跳过这个ZIP区域
        return null;
      }

      // 合并属性
      return {
        ...zipFeature,
        properties: {
          ...zipFeature.properties,
          ...daycareInfo,
          zip_code: zipCode, // 确保ZIP码字段统一
        },
      };
    })
    .filter((feature: any) => feature !== null); // 移除没有托儿所数据的ZIP区域

  console.log(`🔄 合并后包含 ${mergedFeatures.length} 个有托儿所数据的ZIP区域`);

  if (mergedFeatures.length > 0) {
    console.log("🔄 合并后数据示例:", {
      zip: mergedFeatures[0]?.properties?.zip_code,
      saturation: mergedFeatures[0]?.properties?.saturation,
      level: mergedFeatures[0]?.properties?.saturation_level,
      geometry: mergedFeatures[0]?.geometry?.type,
    });
  }

  return {
    type: "FeatureCollection",
    features: mergedFeatures,
    metadata: {
      source: "merged_zip_boundaries_daycare",
      total_features: mergedFeatures.length,
      original_zip_boundaries: zipBoundariesData.features.length,
      original_daycare_data: daycareData.features.length,
      timestamp: new Date().toISOString(),
    },
  };
}

// 合并ZIP边界数据和托儿所属性数据（推荐方法）
export function mergeZipBoundariesWithPropertiesData(
  zipBoundariesData: any,
  daycarePropertiesData: any
): any {
  console.log("🔄 开始合并ZIP边界和托儿所属性数据...");
  console.log("🔄 输入数据检查:", {
    zipBoundariesData: !!zipBoundariesData,
    zipFeatures: zipBoundariesData?.features?.length || 0,
    daycarePropertiesData: !!daycarePropertiesData,
    daycareZipCodes: Object.keys(daycarePropertiesData || {}).length,
  });

  if (!zipBoundariesData?.features || !daycarePropertiesData) {
    console.error(
      "❌ Invalid data provided to mergeZipBoundariesWithPropertiesData"
    );
    return null;
  }

  // 检查ZIP边界数据的属性结构
  const firstZipFeature = zipBoundariesData.features[0];
  console.log("🔄 ZIP边界数据示例属性:", {
    ZCTA5CE10: firstZipFeature?.properties?.ZCTA5CE10,
    zipCode: firstZipFeature?.properties?.zipCode,
    zip_code: firstZipFeature?.properties?.zip_code,
    ZIP: firstZipFeature?.properties?.ZIP,
    allProps: Object.keys(firstZipFeature?.properties || {}),
  });

  // 显示一些属性数据示例
  const sampleZips = Object.keys(daycarePropertiesData).slice(0, 3);
  sampleZips.forEach((zip, index) => {
    console.log(`🔄 托儿所属性数据示例 ${index + 1}:`, {
      zip: zip,
      saturation: daycarePropertiesData[zip]?.saturation,
      level: daycarePropertiesData[zip]?.saturation_level,
    });
  });

  // 合并ZIP边界和托儿所属性数据
  const mergedFeatures = zipBoundariesData.features
    .map((zipFeature: any) => {
      const zipCode =
        zipFeature.properties?.ZCTA5CE10 ||
        zipFeature.properties?.zipCode ||
        zipFeature.properties?.zip_code ||
        zipFeature.properties?.ZIP;

      if (!zipCode) {
        return null;
      }

      const daycareInfo = daycarePropertiesData[zipCode];

      if (!daycareInfo) {
        // 如果没有托儿所数据，跳过这个ZIP区域
        return null;
      }

      // 合并属性
      return {
        ...zipFeature,
        properties: {
          ...zipFeature.properties,
          ...daycareInfo,
          zip_code: zipCode, // 确保ZIP码字段统一
        },
      };
    })
    .filter((feature: any) => feature !== null); // 移除没有托儿所数据的ZIP区域

  console.log(`🔄 合并后包含 ${mergedFeatures.length} 个有托儿所数据的ZIP区域`);

  if (mergedFeatures.length > 0) {
    console.log("🔄 合并后数据示例:", {
      zip: mergedFeatures[0]?.properties?.zip_code,
      saturation: mergedFeatures[0]?.properties?.saturation,
      level: mergedFeatures[0]?.properties?.saturation_level,
      geometry: mergedFeatures[0]?.geometry?.type,
    });
  }

  return {
    type: "FeatureCollection",
    features: mergedFeatures,
    metadata: {
      source: "merged_zip_boundaries_properties",
      total_features: mergedFeatures.length,
      original_zip_boundaries: zipBoundariesData.features.length,
      original_daycare_properties: Object.keys(daycarePropertiesData).length,
      timestamp: new Date().toISOString(),
    },
  };
}
