"use client";

import { useCallback, useEffect, useState } from "react";
import useConfigStore from "@/store/useConfig";
import type { Theme, ResolvedTheme } from "@/types";

export function useTheme() {
  const { theme: storeTheme, setTheme: setStoreTheme } = useConfigStore();
  const [resolvedTheme, setResolvedTheme] = useState<ResolvedTheme>("dark");

  // 使用 store 中的主题，但需要转换类型（store 只支持 light/dark，不支持 system）
  const theme: Theme = storeTheme;

  // 应用主题到 DOM
  const applyTheme = useCallback((newTheme: "light" | "dark") => {
    if (typeof window === "undefined") {
      return;
    }

    const root = document.documentElement;
    root.setAttribute("data-theme", newTheme);
    setResolvedTheme(newTheme);
  }, []);

  // 切换主题
  const toggleTheme = () => {
    const newTheme = theme === "light" ? "dark" : "light";
    changeTheme(newTheme);
  };

  // 改变主题
  const changeTheme = (newTheme: "light" | "dark") => {
    setStoreTheme(newTheme);
    applyTheme(newTheme);
  };

  // 监听 store 主题变化并应用到 DOM
  useEffect(() => {
    applyTheme(theme);
  }, [theme, applyTheme]);

  return {
    theme,
    resolvedTheme,
    setTheme: changeTheme,
    toggleTheme,
  };
}
