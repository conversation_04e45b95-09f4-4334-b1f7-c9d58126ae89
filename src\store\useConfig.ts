import { create } from "zustand";
import { immer } from "zustand/middleware/immer";
// import { ENV } from "@/config/env";
import { SUPPORTED_LANGUAGES } from "@/lib/i18n";
interface ConfigStore {
  navPosition: "left" | "top" | "all"; // 导航位置- 左侧、顶部、全部
  theme: "light" | "dark"; // 顶栏主题-亮色、暗色
  language: SUPPORTED_LANGUAGES; // 语言-中文、英文、日文
  setLanguage: (lang: SUPPORTED_LANGUAGES) => void;
  setTheme: (theme: "light" | "dark") => void;
}
const useHomeStore = create<ConfigStore>()(
  immer(
    (set): ConfigStore => ({
      navPosition: "left",
      theme: "light",
      language: SUPPORTED_LANGUAGES["en-US"],
      setLanguage(lang) {
        set({
          language: lang,
        });
        // 同步更新 i18n 语言（但不在这里调用，让 I18nProvider 处理）
        // 移除这里的 i18n.changeLanguage 调用，避免循环
      },
      setTheme(theme) {
        set({
          theme: theme,
        });
        // 同步更新 DOM 主题属性
        if (typeof window !== "undefined") {
          document.documentElement.setAttribute("data-theme", theme);
          localStorage.setItem("theme", theme);
        }
      },
    })
  )
);

export default useHomeStore;
