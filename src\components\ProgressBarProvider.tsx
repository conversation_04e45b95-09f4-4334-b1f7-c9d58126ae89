"use client";

import { usePathname, useSearchParams } from "next/navigation";
import NProgress from "nprogress";
import { useEffect, useRef } from "react";
import "nprogress/nprogress.css";

export default function ProgressBarProvider() {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // 配置 NProgress
    NProgress.configure({
      showSpinner: false,
      minimum: 0.1,
      speed: 300,
      trickleSpeed: 200,
    });

    // 清理函数
    return () => {
      NProgress.done();
    };
  }, []);

  useEffect(() => {
    // 开始进度条
    NProgress.start();

    // 设置一个延迟来完成进度条
    timeoutRef.current = setTimeout(() => {
      NProgress.done();
    }, 500);

    // 清理函数
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      NProgress.done();
    };
  }, [pathname, searchParams]);

  return null;
}
