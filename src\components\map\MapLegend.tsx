"use client";

import { ChevronUpIcon, ChevronDownIcon } from "@heroicons/react/24/outline";
import React, { useState } from "react";
import { useMapLanguage } from "@/hooks/useMapLanguage";
import type { DaycareStats, LegendItem } from "@/types/daycare";
import { SATURATION_LEVELS, DEFAULT_COLORS } from "@/types/daycare";

interface MapLegendProps {
  stats: DaycareStats | null;
  className?: string;
}

/**
 * 地图图例组件 - 显示饱和度等级和统计信息
 */
const MapLegend: React.FC<MapLegendProps> = ({ stats, className = "" }) => {
  const { t } = useMapLanguage();
  const [isExpanded, setIsExpanded] = useState(true);

  // 如果stats为空，显示加载状态
  if (!stats) {
    return (
      <div className={`map-legend ${className}`}>
        <div className="p-4 text-center">
          <div
            className="animate-pulse text-sm"
            style={{ color: "var(--text-secondary)" }}
          >
            {t("map:loading.loadingStats", "加载统计数据中...")}
          </div>
        </div>
      </div>
    );
  }

  // 安全地获取level_distribution，提供默认值
  const levelDistribution = stats.level_distribution || {
    low: 0,
    medium: 0,
    high: 0,
    very_high: 0,
  };

  // 生成图例项
  const legendItems: LegendItem[] = [
    {
      level: "low",
      color: DEFAULT_COLORS.low,
      label: t("map:legend.lowSaturation"),
      count: levelDistribution.low || 0,
      range: SATURATION_LEVELS.low.range,
    },
    {
      level: "medium",
      color: DEFAULT_COLORS.medium,
      label: t("map:legend.mediumSaturation"),
      count: levelDistribution.medium || 0,
      range: SATURATION_LEVELS.medium.range,
    },
    {
      level: "high",
      color: DEFAULT_COLORS.high,
      label: t("map:legend.highSaturation"),
      count: levelDistribution.high || 0,
      range: SATURATION_LEVELS.high.range,
    },
    {
      level: "very-high",
      color: DEFAULT_COLORS["very-high"],
      label: t("map:legend.veryHighSaturation"),
      count: levelDistribution.very_high || 0,
      range: SATURATION_LEVELS["very-high"].range,
    },
  ];

  // 计算百分比
  const getPercentage = (count: number): number => {
    const totalZipCodes = stats.total_zip_codes || 1; // 避免除零错误
    return Math.round((count / totalZipCodes) * 100);
  };

  // 获取市场状态描述
  const getMarketStatus = (): {
    status: string;
    color: string;
    description: string;
  } => {
    const highSaturationPercentage = getPercentage(
      stats.level_distribution.high + stats.level_distribution.very_high
    );

    if (highSaturationPercentage > 70) {
      return {
        status: t("map:legend.marketOverSaturated"),
        color: "#ef4444", // 使用直接颜色值而不是 Tailwind 类
        description: t("map:legend.mostAreasCompetitive"),
      };
    } else if (highSaturationPercentage > 50) {
      return {
        status: t("map:legend.highCompetition"),
        color: "#f97316", // 橙色
        description: t("map:legend.majorityAreasCompetitive"),
      };
    } else if (highSaturationPercentage > 30) {
      return {
        status: t("map:legend.moderateCompetition"),
        color: "#eab308", // 黄色
        description: t("map:legend.moderateCompetitionLevel"),
      };
    } else {
      return {
        status: t("map:legend.growthOpportunity"),
        color: "#22c55e", // 绿色
        description: t("map:legend.marketOpportunityExists"),
      };
    }
  };

  const marketStatus = getMarketStatus();

  return (
    <div
      className={`legend-container ${className}`}
      style={{
        background: "var(--surface)",
        borderRadius: "var(--radius)",
        boxShadow: "var(--shadow-lg)",
        border: "1px solid var(--border)",
        color: "var(--text-primary)",
      }}
    >
      {/* 图例标题 */}
      <div
        className="flex items-center justify-between p-4 cursor-pointer"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <h3
          className="text-lg font-semibold"
          style={{ color: "var(--text-primary)" }}
        >
          {t("map:legend.title")}
        </h3>
        {isExpanded ? (
          <ChevronUpIcon
            className="h-5 w-5"
            style={{ color: "var(--text-secondary)" }}
          />
        ) : (
          <ChevronDownIcon
            className="h-5 w-5"
            style={{ color: "var(--text-secondary)" }}
          />
        )}
      </div>

      {/* 图例内容 */}
      {isExpanded && (
        <div className="px-4 pb-4">
          {/* 饱和度等级 */}
          <div className="space-y-3 mb-4">
            {legendItems.map((item) => (
              <div
                key={item.level}
                className="flex items-center justify-between"
              >
                <div className="flex items-center space-x-3">
                  {/* 颜色圆点 */}
                  <div
                    className="w-4 h-4 rounded-full border-2 border-white shadow-sm"
                    style={{ backgroundColor: item.color }}
                  />

                  {/* 标签和范围 */}
                  <div className="flex flex-col">
                    <span
                      className="text-sm font-medium"
                      style={{ color: "var(--text-primary)" }}
                    >
                      {item.label}
                    </span>
                    <span
                      className="text-xs"
                      style={{ color: "var(--text-secondary)" }}
                    >
                      ({item.range})
                    </span>
                  </div>
                </div>

                {/* 数量和百分比 */}
                <div className="text-right">
                  <div
                    className="text-sm font-medium"
                    style={{ color: "var(--text-primary)" }}
                  >
                    {(item.count || 0).toLocaleString()}
                  </div>
                  <div
                    className="text-xs"
                    style={{ color: "var(--text-secondary)" }}
                  >
                    {getPercentage(item.count)}%
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* 分隔线 */}
          <div
            className="my-4"
            style={{ borderTop: "1px solid var(--border)" }}
          ></div>

          {/* 总计信息 */}
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span
                className="text-sm font-medium"
                style={{ color: "var(--text-secondary)" }}
              >
                {t("map:legend.totalZipCodes")}
              </span>
              <span
                className="text-sm font-semibold"
                style={{ color: "var(--text-primary)" }}
              >
                {(stats.total_zip_codes || 0).toLocaleString()}
              </span>
            </div>

            <div className="flex justify-between items-center">
              <span
                className="text-sm font-medium"
                style={{ color: "var(--text-secondary)" }}
              >
                {t("map:legend.averageSaturation")}
              </span>
              <span
                className="text-sm font-semibold"
                style={{ color: "var(--text-primary)" }}
              >
                {((stats.saturation?.avg || 0) * 100).toFixed(1)}%
              </span>
            </div>
          </div>

          {/* 分隔线 */}
          <div
            className="my-4"
            style={{ borderTop: "1px solid var(--border)" }}
          ></div>

          {/* 市场状态 */}
          <div
            className="rounded-lg p-3"
            style={{
              background: "var(--surface-light)",
              borderRadius: "var(--radius)",
            }}
          >
            <div className="flex items-center justify-between mb-2">
              <span
                className="text-sm font-medium"
                style={{ color: "var(--text-secondary)" }}
              >
                {t("map:legend.marketStatus")}
              </span>
              <span
                className="text-sm font-semibold"
                style={{ color: marketStatus.color }}
              >
                {marketStatus.status}
              </span>
            </div>
            <p className="text-xs" style={{ color: "var(--text-secondary)" }}>
              {marketStatus.description}
            </p>
          </div>

          {/* 圆点大小说明 */}
          <div
            className="mt-4 pt-3"
            style={{ borderTop: "1px solid var(--border)" }}
          >
            <h4
              className="text-sm font-medium mb-2"
              style={{ color: "var(--text-secondary)" }}
            >
              {t("map:legend.circleSizeExplanation")}
            </h4>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div
                  className="w-2 h-2 rounded-full"
                  style={{ background: "var(--text-tertiary)" }}
                ></div>
                <span
                  className="text-xs"
                  style={{ color: "var(--text-secondary)" }}
                >
                  {t("map:legend.lowSaturation")}
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <div
                  className="w-3 h-3 rounded-full"
                  style={{ background: "var(--text-tertiary)" }}
                ></div>
                <span
                  className="text-xs"
                  style={{ color: "var(--text-secondary)" }}
                >
                  {t("map:legend.mediumSaturation")}
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <div
                  className="w-4 h-4 rounded-full"
                  style={{ background: "var(--text-tertiary)" }}
                ></div>
                <span
                  className="text-xs"
                  style={{ color: "var(--text-secondary)" }}
                >
                  {t("map:legend.highSaturation")}
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <div
                  className="w-5 h-5 rounded-full"
                  style={{ background: "var(--text-tertiary)" }}
                ></div>
                <span
                  className="text-xs"
                  style={{ color: "var(--text-secondary)" }}
                >
                  {t("map:legend.veryHighSaturation")}
                </span>
              </div>
            </div>
          </div>

          {/* 数据来源 */}
          <div
            className="mt-3 pt-3"
            style={{ borderTop: "1px solid var(--border)" }}
          >
            <p
              className="text-xs text-center"
              style={{ color: "var(--text-tertiary)" }}
            >
              {t("map:legend.dataSource")}
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default MapLegend;
