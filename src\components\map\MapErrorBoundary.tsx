"use client";

import {
  ExclamationTriangleIcon,
  ArrowPathIcon,
} from "@heroicons/react/24/outline";
import React, { Component, ErrorInfo, ReactNode } from "react";
import { useMapLanguage } from "@/hooks/useMapLanguage";

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

/**
 * 错误UI组件 - 使用多语言支持
 */
const ErrorUI: React.FC<{
  error: Error | null;
  errorInfo: ErrorInfo | null;
  onRetry: () => void;
}> = ({ error, errorInfo, onRetry }) => {
  const { t } = useMapLanguage();

  return (
    <div className="w-full h-full flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full mx-4">
        <div className="bg-white rounded-lg shadow-lg p-6 text-center">
          {/* 错误图标 */}
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
            <ExclamationTriangleIcon className="h-6 w-6 text-red-600" />
          </div>

          {/* 错误标题 */}
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {t("map:error.mapLoadFailed")}
          </h3>

          {/* 错误描述 */}
          <p className="text-sm text-gray-500 mb-6">
            {t("map:error.mapLoadFailedDesc")}
          </p>

          {/* 错误详情（开发环境） */}
          {process.env.NODE_ENV === "development" && error && (
            <div className="mb-6 p-4 bg-gray-100 rounded-lg text-left">
              <h4 className="text-sm font-medium text-gray-900 mb-2">
                {t("map:error.errorDetails")}
              </h4>
              <pre className="text-xs text-gray-700 overflow-auto max-h-32">
                {error.toString()}
                {errorInfo?.componentStack}
              </pre>
            </div>
          )}

          {/* 操作按钮 */}
          <div className="flex flex-col sm:flex-row gap-3">
            <button
              onClick={onRetry}
              className="flex-1 inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <ArrowPathIcon className="h-4 w-4 mr-2" />
              {t("map:error.reload")}
            </button>

            <button
              onClick={() => window.location.reload()}
              className="flex-1 inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              {t("map:error.refreshPage")}
            </button>
          </div>

          {/* 帮助信息 */}
          <div className="mt-6 pt-4 border-t border-gray-200">
            <h4 className="text-sm font-medium text-gray-900 mb-2">
              {t("map:error.possibleSolutions")}
            </h4>
            <ul className="text-xs text-gray-600 text-left space-y-1">
              <li>{t("map:error.checkNetwork")}</li>
              <li>{t("map:error.checkMapboxToken")}</li>
              <li>{t("map:error.tryRefresh")}</li>
              <li>{t("map:error.contactSupport")}</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

/**
 * 地图错误边界组件 - 捕获和处理地图相关错误
 */
class MapErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): State {
    // 更新 state 使下一次渲染能够显示降级后的 UI
    return {
      hasError: true,
      error,
      errorInfo: null,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // 记录错误信息
    console.error("Map Error Boundary caught an error:", error, errorInfo);

    this.setState({
      error,
      errorInfo,
    });

    // 这里可以将错误日志上报给错误监控服务
    // reportError(error, errorInfo);
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  render() {
    if (this.state.hasError) {
      // 如果提供了自定义fallback，使用它
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // 默认错误UI
      return (
        <ErrorUI
          error={this.state.error}
          errorInfo={this.state.errorInfo}
          onRetry={this.handleRetry}
        />
      );
    }

    return this.props.children;
  }
}

/**
 * 函数式组件版本的错误边界（用于特定场景）
 */
export const MapErrorFallback: React.FC<{
  error?: Error;
  onRetry?: () => void;
}> = ({ error, onRetry }) => {
  const { t } = useMapLanguage();

  return (
    <div className="w-full h-64 flex items-center justify-center bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
      <div className="text-center">
        <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          {t("map:error.mapLoadFailed")}
        </h3>
        <p className="text-sm text-gray-500 mb-4">
          {error?.message || t("map:error.componentError")}
        </p>
        {onRetry && (
          <button
            onClick={onRetry}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            <ArrowPathIcon className="h-4 w-4 mr-2" />
            {t("map:error.retry")}
          </button>
        )}
      </div>
    </div>
  );
};

export default MapErrorBoundary;
