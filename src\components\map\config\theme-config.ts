/**
 * 地图主题配置
 * 集中管理所有主题相关的配置和样式
 */

import type { Expression } from "mapbox-gl";

/**
 * 地图样式配置
 */
export const MAP_STYLES = {
  light: "mapbox://styles/mapbox/light-v11",
  dark: "mapbox://styles/mapbox/dark-v11",
} as const;

/**
 * 主题颜色配置
 */
export const THEME_COLORS = {
  light: {
    // 基础颜色
    primary: "#3b82f6",
    secondary: "#6b7280",
    success: "#22c55e",
    warning: "#eab308",
    danger: "#ef4444",

    // 表面颜色
    surface: "#ffffff",
    surfaceLight: "#f8fafc",
    surfaceDark: "#f1f5f9",

    // 边框和分割线
    border: "#e2e8f0",
    borderLight: "#f1f5f9",

    // 文本颜色
    textPrimary: "#1e293b",
    textSecondary: "#64748b",
    textMuted: "#94a3b8",

    // 阴影
    shadow: "rgba(0, 0, 0, 0.1)",
    shadowMedium: "rgba(0, 0, 0, 0.15)",
    shadowLarge: "rgba(0, 0, 0, 0.2)",
  },
  dark: {
    // 基础颜色
    primary: "#60a5fa",
    secondary: "#9ca3af",
    success: "#34d399",
    warning: "#fbbf24",
    danger: "#f87171",

    // 表面颜色
    surface: "#1e293b",
    surfaceLight: "#334155",
    surfaceDark: "#0f172a",

    // 边框和分割线
    border: "#475569",
    borderLight: "#64748b",

    // 文本颜色
    textPrimary: "#f1f5f9",
    textSecondary: "#cbd5e1",
    textMuted: "#94a3b8",

    // 阴影
    shadow: "rgba(0, 0, 0, 0.3)",
    shadowMedium: "rgba(0, 0, 0, 0.4)",
    shadowLarge: "rgba(0, 0, 0, 0.5)",
  },
} as const;

/**
 * 图层主题配置
 */
export const LAYER_THEME_CONFIG = {
  light: {
    zipAreas: {
      fillColor: THEME_COLORS.light.primary,
      fillOpacity: 0.15,
      strokeColor: THEME_COLORS.light.surface,
      strokeWidth: 1.5,
      strokeOpacity: 0.9,
    },
    zipBoundaries: {
      fillColor: THEME_COLORS.light.primary,
      fillOpacity: 0.1,
      lineColor: THEME_COLORS.light.primary,
      lineOpacity: 0.8,
    },
    circles: {
      strokeColor: THEME_COLORS.light.surface,
      strokeWidth: 2,
      colors: {
        low: THEME_COLORS.light.success,
        medium: THEME_COLORS.light.warning,
        high: "#f97316", // orange
        "very-high": THEME_COLORS.light.danger,
        default: THEME_COLORS.light.secondary,
      },
    },
    labels: {
      color: THEME_COLORS.light.textPrimary,
      haloColor: THEME_COLORS.light.surface,
      haloWidth: 2,
    },
  },
  dark: {
    zipAreas: {
      fillColor: THEME_COLORS.dark.primary,
      fillOpacity: 0.2,
      strokeColor: THEME_COLORS.dark.border,
      strokeWidth: 1.5,
      strokeOpacity: 0.8,
    },
    zipBoundaries: {
      fillColor: THEME_COLORS.dark.primary,
      fillOpacity: 0.15,
      lineColor: THEME_COLORS.dark.primary,
      lineOpacity: 0.9,
    },
    circles: {
      strokeColor: THEME_COLORS.dark.border,
      strokeWidth: 2,
      colors: {
        low: THEME_COLORS.dark.success,
        medium: THEME_COLORS.dark.warning,
        high: "#fb923c", // orange
        "very-high": THEME_COLORS.dark.danger,
        default: THEME_COLORS.dark.secondary,
      },
    },
    labels: {
      color: THEME_COLORS.dark.textPrimary,
      haloColor: THEME_COLORS.dark.surface,
      haloWidth: 2,
    },
  },
} as const;

/**
 * 控件主题配置
 */
export const CONTROL_THEME_CONFIG = {
  light: {
    backgroundColor: THEME_COLORS.light.surface,
    borderColor: THEME_COLORS.light.border,
    textColor: THEME_COLORS.light.textPrimary,
    hoverColor: THEME_COLORS.light.surfaceLight,
    shadowColor: THEME_COLORS.light.shadow,
  },
  dark: {
    backgroundColor: THEME_COLORS.dark.surface,
    borderColor: THEME_COLORS.dark.border,
    textColor: THEME_COLORS.dark.textPrimary,
    hoverColor: THEME_COLORS.dark.surfaceLight,
    shadowColor: THEME_COLORS.dark.shadow,
  },
} as const;

/**
 * 弹窗主题配置
 */
export const POPUP_THEME_CONFIG = {
  light: {
    backgroundColor: THEME_COLORS.light.surface,
    borderColor: THEME_COLORS.light.border,
    textColor: THEME_COLORS.light.textPrimary,
    shadowColor: THEME_COLORS.light.shadowLarge,
    borderRadius: "12px",
  },
  dark: {
    backgroundColor: THEME_COLORS.dark.surface,
    borderColor: THEME_COLORS.dark.border,
    textColor: THEME_COLORS.dark.textPrimary,
    shadowColor: THEME_COLORS.dark.shadowLarge,
    borderRadius: "12px",
  },
} as const;

/**
 * 响应式断点配置
 */
export const BREAKPOINTS = {
  mobile: 768,
  tablet: 1024,
  desktop: 1280,
} as const;

/**
 * 动画配置
 */
export const ANIMATION_CONFIG = {
  duration: {
    fast: 150,
    normal: 300,
    slow: 500,
  },
  easing: {
    ease: "ease",
    easeIn: "ease-in",
    easeOut: "ease-out",
    easeInOut: "ease-in-out",
  },
} as const;

/**
 * 地图缩放级别配置
 */
export const ZOOM_CONFIG = {
  min: 6,
  max: 18,
  default: 7,
  boundaries: {
    show: 6,
    labels: 9,
  },
} as const;

/**
 * 性能优化配置
 */
export const PERFORMANCE_CONFIG = {
  // 图层简化容差
  tolerance: 0.375,
  // 缓冲区大小
  buffer: 0,
  // 最大要素数量
  maxFeatures: 10000,
  // 批处理大小
  batchSize: 100,
} as const;

/**
 * 辅助功能配置
 */
export const ACCESSIBILITY_CONFIG = {
  // 高对比度模式
  highContrast: {
    borderWidth: 2,
    strokeWidth: 3,
  },
  // 减少动画模式
  reducedMotion: {
    duration: 0,
    enabled: false,
  },
  // 键盘导航
  keyboard: {
    enabled: true,
    focusOutlineWidth: 2,
  },
} as const;

/**
 * 获取当前主题的配置
 */
export const getThemeConfig = (theme: "light" | "dark") => ({
  mapStyle: MAP_STYLES[theme],
  colors: THEME_COLORS[theme],
  layers: LAYER_THEME_CONFIG[theme],
  controls: CONTROL_THEME_CONFIG[theme],
  popup: POPUP_THEME_CONFIG[theme],
});

/**
 * 创建主题感知的 Mapbox 表达式
 */
export const createThemeExpression = (
  property: string,
  valueMap: Record<string, any>,
  defaultValue: any = "#6b7280"
): Expression => {
  return [
    "match",
    ["get", property],
    ...Object.entries(valueMap).flatMap(([key, value]) => [key, value]),
    defaultValue,
  ] as Expression;
};

/**
 * 创建缩放级别插值表达式
 */
export const createZoomExpression = (
  stops: Array<[number, any]>
): Expression => {
  return ["interpolate", ["linear"], ["zoom"], ...stops.flat()] as Expression;
};

/**
 * 导出所有配置
 */
export const themeConfig = {
  MAP_STYLES,
  THEME_COLORS,
  LAYER_THEME_CONFIG,
  CONTROL_THEME_CONFIG,
  POPUP_THEME_CONFIG,
  BREAKPOINTS,
  ANIMATION_CONFIG,
  ZOOM_CONFIG,
  PERFORMANCE_CONFIG,
  ACCESSIBILITY_CONFIG,
  getThemeConfig,
  createThemeExpression,
  createZoomExpression,
};

export default themeConfig;
