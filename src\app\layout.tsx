import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON> } from "next/font/google";
import { Suspense } from "react";
import "./globals.css";
import "mapbox-gl/dist/mapbox-gl.css";
import ErrorBoundary from "@/components/ErrorBoundary";
import ProgressBarProvider from "@/components/ProgressBarProvider";
import I18nProvider from "@/provider/I18nProvider";
import { RoutePerformanceProvider } from "@/provider/RoutePerformanceProvider";
import ThemeProvider from "@/provider/ThemeProvider";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
  display: "swap",
  fallback: [
    "system-ui",
    "-apple-system",
    "BlinkMacSystemFont",
    "Segoe UI",
    "Roboto",
    "sans-serif",
  ],
});

const geistMono = GeistMono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
  display: "swap",
  fallback: [
    "ui-monospace",
    "SFMono-Regular",
    "Menlo",
    "Monaco",
    "Consolas",
    "Liberation Mono",
    "Courier New",
    "monospace",
  ],
});

export const metadata: Metadata = {
  title: "Map",
  description: "Data Visualization Map Application",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <Suspense fallback={null}>
          <ProgressBarProvider />
        </Suspense>
        <ErrorBoundary>
          <ThemeProvider>
            <I18nProvider>
              <RoutePerformanceProvider>{children}</RoutePerformanceProvider>
            </I18nProvider>
          </ThemeProvider>
        </ErrorBoundary>
      </body>
    </html>
  );
}
