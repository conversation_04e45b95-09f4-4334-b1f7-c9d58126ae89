"use client";

import { usePathname } from "next/navigation";
import { useEffect, createContext, useContext, ReactNode } from "react";

interface RoutePerformanceContextType {
  currentRoute: string;
  routeLoadTime: number;
}

const RoutePerformanceContext = createContext<RoutePerformanceContextType>({
  currentRoute: "/",
  routeLoadTime: 0,
});

export const useRoutePerformance = () => {
  return useContext(RoutePerformanceContext);
};

interface RoutePerformanceProviderProps {
  children: ReactNode;
}

export const RoutePerformanceProvider = ({
  children,
}: RoutePerformanceProviderProps) => {
  const pathname = usePathname();

  useEffect(() => {
    // 标记路由开始
    performance.mark("route-start");
    const timer = setTimeout(() => {
      // 标记路由结束并测量
      performance.mark("route-end");
      performance.measure("route-duration", "route-start", "route-end");
      // 开发环境下输出性能信息
    }, 0);
    return () => {
      clearTimeout(timer);
      // 清理性能标记
      try {
        performance.clearMarks("route-start");
        performance.clearMarks("route-end");
        performance.clearMeasures("route-duration");
      } catch (e) {
        console.error("Performance API cleanup failed:", e);
        // 忽略清理错误
      }
    };
  }, [pathname]);

  const value = {
    currentRoute: pathname ?? "/",
    routeLoadTime: 0, // 可以在这里计算实际的加载时间
  };

  return (
    <RoutePerformanceContext.Provider value={value}>
      {children}
    </RoutePerformanceContext.Provider>
  );
};
