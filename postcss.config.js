module.exports = {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
    "postcss-pxtorem": {
      rootValue: 16, // 根字体大小，通常为 16px
      unitPrecision: 5, // rem 精度，小数点后几位
      propList: ["*"], // 转换哪些属性的 px，['*'] 表示所有属性
      selectorBlackList: [], // 要忽略的选择器，可以在此处定义不转换的类或元素
      replace: true, // 直接替换 px 而不是保留 px 和 rem 两种单位
      mediaQuery: false, // 是否转换媒体查询中的 px
      minPixelValue: 0, // 需要转换的最小 px 值
    },
  },
};
