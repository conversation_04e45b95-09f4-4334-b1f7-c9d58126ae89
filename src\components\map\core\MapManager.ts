import mapboxgl from "mapbox-gl";
import { CALIFORNIA_CENTER, CALIFORNIA_BOUNDS } from "@/types/daycare";

/**
 * 地图主题配置
 */
export interface MapThemeConfig {
  style: string;
  controlsStyle: {
    backgroundColor: string;
    borderColor: string;
    textColor: string;
    hoverColor: string;
  };
}

/**
 * 地图主题样式配置
 */
export const MAP_THEMES: Record<"light" | "dark", MapThemeConfig> = {
  light: {
    style: "mapbox://styles/mapbox/light-v11",
    controlsStyle: {
      backgroundColor: "var(--surface)",
      borderColor: "var(--border)",
      textColor: "var(--text-primary)",
      hoverColor: "var(--surface-light)",
    },
  },
  dark: {
    style: "mapbox://styles/mapbox/dark-v11",
    controlsStyle: {
      backgroundColor: "var(--surface)",
      borderColor: "var(--border)",
      textColor: "var(--text-primary)",
      hoverColor: "var(--surface-light)",
    },
  },
};

/**
 * 地图核心管理器
 * 负责地图实例的初始化、配置和生命周期管理
 * 支持主题适配
 */
export class MapManager {
  private map: mapboxgl.Map | null = null;
  private container: HTMLDivElement | null = null;
  private currentTheme: "light" | "dark" = "dark";

  constructor() {
    this.initializeMapbox();
  }

  /**
   * 初始化 Mapbox 配置
   */
  private initializeMapbox(): void {
    if (!process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN) {
      throw new Error("Mapbox access token is required");
    }
    mapboxgl.accessToken = process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN;
  }

  /**
   * 获取当前主题
   */
  private getCurrentTheme(): "light" | "dark" {
    if (typeof window === "undefined") return "dark";

    const theme = document.documentElement.getAttribute("data-theme");
    return (theme as "light" | "dark") || "dark";
  }

  /**
   * 创建地图实例
   */
  createMap(container: HTMLDivElement): mapboxgl.Map {
    if (this.map) {
      this.destroyMap();
    }

    this.currentTheme = this.getCurrentTheme();
    this.container = container;

    this.map = new mapboxgl.Map({
      container,
      style: MAP_THEMES[this.currentTheme].style,
      center: CALIFORNIA_CENTER,
      zoom: 7,
      maxBounds: CALIFORNIA_BOUNDS,
      attributionControl: false,
    });

    this.addControls();
    this.setupThemeObserver();
    return this.map;
  }

  /**
   * 设置主题观察器
   */
  private setupThemeObserver(): void {
    if (typeof window === "undefined") return;

    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (
          mutation.type === "attributes" &&
          mutation.attributeName === "data-theme"
        ) {
          const newTheme = this.getCurrentTheme();
          if (newTheme !== this.currentTheme) {
            this.updateMapTheme(newTheme);
          }
        }
      });
    });

    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ["data-theme"],
    });
  }

  /**
   * 更新地图主题（优化版本）
   */
  private updateMapTheme(theme: "light" | "dark"): void {
    if (!this.map) return;

    this.currentTheme = theme;
    const themeConfig = MAP_THEMES[theme];

    // 保存当前的数据源和图层信息
    const currentSources = this.map.getStyle().sources;
    const currentLayers = this.map.getStyle().layers;

    // 隐藏地图画布以减少重绘
    const canvas = this.map.getCanvas();
    const originalVisibility = canvas.style.visibility;
    canvas.style.visibility = "hidden";

    // 更新地图样式
    this.map.setStyle(themeConfig.style);

    // 等待样式完全加载完成后重新添加图层
    this.map.once("style.load", () => {
      // 立即触发事件，无延迟
      this.map?.fire("themeChanged", {
        theme,
        sources: currentSources,
        layers: currentLayers,
      });

      // 恢复画布可见性
      canvas.style.visibility = originalVisibility;
    });

    // 更新控件样式
    this.updateControlsTheme(themeConfig);
  }

  /**
   * 更新控件主题样式
   */
  private updateControlsTheme(themeConfig: MapThemeConfig): void {
    if (typeof window === "undefined") return;

    // 更新导航控件样式
    const navControls = document.querySelectorAll(".mapboxgl-ctrl-group");
    navControls.forEach((control) => {
      const element = control as HTMLElement;
      element.style.backgroundColor = themeConfig.controlsStyle.backgroundColor;
      element.style.borderColor = themeConfig.controlsStyle.borderColor;
      element.style.color = themeConfig.controlsStyle.textColor;
    });

    // 更新比例尺样式
    const scaleControls = document.querySelectorAll(".mapboxgl-ctrl-scale");
    scaleControls.forEach((control) => {
      const element = control as HTMLElement;
      element.style.backgroundColor = themeConfig.controlsStyle.backgroundColor;
      element.style.borderColor = themeConfig.controlsStyle.borderColor;
      element.style.color = themeConfig.controlsStyle.textColor;
    });
  }

  /**
   * 添加地图控件
   */
  private addControls(): void {
    if (!this.map) return;

    // 添加导航控件
    const navControl = new mapboxgl.NavigationControl();
    this.map.addControl(navControl, "top-right");

    // 添加比例尺
    const scaleControl = new mapboxgl.ScaleControl({
      maxWidth: 100,
      unit: "metric",
    });
    this.map.addControl(scaleControl, "bottom-right");

    // 应用当前主题样式
    this.map.on("style.load", () => {
      this.updateControlsTheme(MAP_THEMES[this.currentTheme]);
    });
  }

  /**
   * 获取地图实例
   */
  getMap(): mapboxgl.Map | null {
    return this.map;
  }

  /**
   * 检查图层是否存在
   */
  hasLayer(layerId: string): boolean {
    return this.map ? this.map.getLayer(layerId) !== undefined : false;
  }

  /**
   * 添加数据源
   */
  addSource(sourceId: string, source: any): void {
    if (!this.map) return;

    if (this.map.getSource(sourceId)) {
      this.map.removeSource(sourceId);
    }
    this.map.addSource(sourceId, source);
  }

  /**
   * 添加图层
   */
  addLayer(layer: any): void {
    if (!this.map) return;

    if (this.map.getLayer(layer.id)) {
      this.map.removeLayer(layer.id);
    }
    this.map.addLayer(layer);
  }

  /**
   * 移除图层
   */
  removeLayer(layerId: string): void {
    if (!this.map) return;

    if (this.map.getLayer(layerId)) {
      this.map.removeLayer(layerId);
    }
  }

  /**
   * 移除数据源
   */
  removeSource(sourceId: string): void {
    if (!this.map) return;

    if (this.map.getSource(sourceId)) {
      this.map.removeSource(sourceId);
    }
  }

  /**
   * 检查地图是否已初始化
   */
  isMapReady(): boolean {
    return this.map !== null && this.map.isStyleLoaded();
  }

  /**
   * 飞行到指定位置
   */
  flyTo(coordinates: [number, number], zoom: number = 12): void {
    if (!this.map) return;

    this.map.flyTo({
      center: coordinates,
      zoom,
      duration: 2000,
    });
  }

  /**
   * 调整地图大小
   * 当容器尺寸发生变化时调用此方法
   */
  resize(): void {
    if (!this.map) return;

    // 使用 requestAnimationFrame 确保在下一帧执行，避免布局抖动
    requestAnimationFrame(() => {
      if (this.map) {
        this.map.resize();
      }
    });
  }

  /**
   * 设置地图事件监听器
   */
  onLoad(callback: () => void): void {
    if (!this.map) return;
    this.map.on("load", callback);
  }

  /**
   * 设置地图错误监听器
   */
  onError(callback: (error: any) => void): void {
    if (!this.map) return;
    this.map.on("error", callback);
  }

  /**
   * 销毁地图实例
   */
  destroyMap(): void {
    if (this.map) {
      this.map.remove();
      this.map = null;
    }
    this.container = null;
  }

  /**
   * 获取地图容器
   */
  getContainer(): HTMLDivElement | null {
    return this.container;
  }

  /**
   * 检查图层是否存在
   */
  hasLayer(layerId: string): boolean {
    return this.map ? !!this.map.getLayer(layerId) : false;
  }

  /**
   * 检查数据源是否存在
   */
  hasSource(sourceId: string): boolean {
    return this.map ? !!this.map.getSource(sourceId) : false;
  }

  /**
   * 移除图层
   */
  removeLayer(layerId: string): void {
    if (this.map && this.hasLayer(layerId)) {
      this.map.removeLayer(layerId);
    }
  }

  /**
   * 移除数据源
   */
  removeSource(sourceId: string): void {
    if (this.map && this.hasSource(sourceId)) {
      this.map.removeSource(sourceId);
    }
  }

  /**
   * 添加数据源
   */
  addSource(sourceId: string, source: mapboxgl.AnySourceData): void {
    if (!this.map) return;
    this.map.addSource(sourceId, source);
  }

  /**
   * 添加图层
   */
  addLayer(layer: mapboxgl.AnyLayer): void {
    if (!this.map) return;
    this.map.addLayer(layer);
  }

  /**
   * 添加点击事件监听器
   */
  onClick(
    layerId: string,
    callback: (
      e: mapboxgl.MapMouseEvent & { features?: mapboxgl.MapboxGeoJSONFeature[] }
    ) => void
  ): void {
    if (!this.map) return;
    this.map.on("click", layerId, callback);
  }

  /**
   * 添加鼠标进入事件监听器
   */
  onMouseEnter(layerId: string, callback: () => void): void {
    if (!this.map) return;
    this.map.on("mouseenter", layerId, callback);
  }

  /**
   * 添加鼠标离开事件监听器
   */
  onMouseLeave(layerId: string, callback: () => void): void {
    if (!this.map) return;
    this.map.on("mouseleave", layerId, callback);
  }

  /**
   * 设置鼠标样式
   */
  setCursor(cursor: string): void {
    if (!this.map) return;
    this.map.getCanvas().style.cursor = cursor;
  }
}
