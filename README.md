# 加州托儿所市场分析平台

基于 Next.js 和 Mapbox GL JS 构建的现代化 React 应用，用于可视化分析加州各 ZIP 码区域的托儿所市场饱和度数据。

## ✨ 核心功能

### 🗺️ 交互式地图

- **Mapbox GL JS 集成**: 高性能、交互式地图，专注于加州地区
- **ZIP 码可视化**: 每个 ZIP 码区域用圆形标记表示
- **颜色编码饱和度**: 直观的市场饱和度等级表示
  - 🟢 绿色: 低饱和度 (≤0.5) - 市场机会
  - 🟡 黄色: 中等饱和度 (0.5-1.0) - 适度竞争
  - 🟠 橙色: 高饱和度 (1.0-2.0) - 高度竞争
  - 🔴 红色: 极高饱和度 (>2.0) - 市场过饱和
- **动态尺寸**: 圆点大小与饱和度等级成正比
- **实时数据**: 基于 2023 年最新数据

### 🔍 智能搜索

- **地址搜索**: 使用 Mapbox 地理编码 API 搜索加州地址
- **自动补全**: 实时搜索建议，自动过滤加州范围
- **地图导航**: 点击搜索结果直接跳转到对应位置
- **搜索历史**: 保存最近搜索记录

### 📊 数据交互

- **详情弹窗**: 点击任意标记查看详细信息
- **综合数据**: 显示 ZIP 码、总容量、总出生数和饱和度
- **数据解读**: 提供饱和度等级的上下文解释
- **可视化指标**: 直观的饱和度等级指示器
- **统计图例**: 实时显示数据分布统计

### 🌐 多语言支持

- **中英文切换**: 完整的界面多语言支持
- **智能回退**: 多层翻译回退机制
- **实时切换**: 无需刷新页面即可切换语言
- **本地化存储**: 记住用户语言偏好

### 🎨 主题系统

- **明暗主题**: 支持明亮和暗黑两种主题模式
- **自动适配**: 根据系统偏好自动选择主题
- **平滑过渡**: 主题切换时的流畅动画效果
- **持久化**: 自动保存用户主题偏好

### 📈 数据管理

- **2023 年数据**: 基于最新的 2023 年加州托儿所市场数据
- **GeoJSON 格式**: 高效的地理数据存储和处理
- **数据验证**: 全面的数据验证和错误报告
- **错误处理**: 健壮的错误边界和回退机制
- **性能优化**: 数据预加载和缓存机制

## 🛠️ 技术栈

### 前端框架

- **React 19**: 最新的 React 版本，支持并发特性
- **Next.js 15**: 全栈 React 框架，支持 SSR/SSG
- **TypeScript**: 类型安全的 JavaScript 超集

### 地图与可视化

- **Mapbox GL JS**: 高性能 WebGL 地图渲染引擎
- **GeoJSON**: 标准地理数据格式
- **自定义图层**: 动态数据可视化

### UI 与样式

- **Tailwind CSS**: 实用优先的 CSS 框架
- **Radix UI**: 无障碍的 UI 组件库
- **Framer Motion**: 流畅的动画库
- **Lucide React**: 现代图标库

### 状态管理

- **Zustand**: 轻量级状态管理库
- **React Hooks**: 内置状态管理
- **Immer**: 不可变状态更新

### 国际化与主题

- **react-i18next**: React 国际化解决方案
- **next-themes**: Next.js 主题管理
- **自定义 Hook**: 多语言和主题切换

### 开发工具

- **ESLint**: 代码质量检查
- **TypeScript**: 严格模式类型检查
- **PostCSS**: CSS 后处理器

## 🚀 快速开始

### 环境要求

- **Node.js 18+**: 推荐使用最新 LTS 版本
- **包管理器**: npm、yarn 或 pnpm
- **Mapbox 账户**: 免费账户即可使用

### 安装步骤

1. **克隆项目并安装依赖**:

   ```bash
   git clone <repository-url>
   cd map
   npm install
   ```

2. **配置 Mapbox API**:

   - 在 [mapbox.com](https://www.mapbox.com/) 注册免费账户
   - 从 Mapbox 控制台获取访问令牌
   - 创建 `.env.local` 文件并添加令牌:

   ```bash
   NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN=your_actual_mapbox_token_here
   ```

3. **处理数据文件**:

   ```bash
   # 生成 2023 年数据
   node data/create_2023_data.js
   ```

4. **启动开发服务器**:

   ```bash
   npm run dev
   ```

5. **打开浏览器**:
   访问 `http://localhost:3000` (或终端显示的端口)

## 🎯 核心特性展示

### 地图可视化

- 🗺️ 基于 Mapbox GL JS 的高性能地图渲染
- 📍 1,269 个加州 ZIP 码区域的精确定位
- 🎨 四级颜色编码的饱和度可视化
- 📏 动态圆点大小反映市场饱和程度

### 交互体验

- 🔍 实时地址搜索和地图导航
- 💬 详细的数据弹窗展示
- 📊 动态统计图例和数据分析
- 🎛️ 流畅的缩放和平移操作

### 用户界面

- 🌓 明暗主题无缝切换
- 🌍 中英文界面完整支持
- 📱 响应式设计适配各种设备
- ⚡ 优化的加载性能和用户体验

### 数据洞察

- 📈 市场饱和度趋势分析
- 🎯 投资机会识别
- 📋 详细的统计数据展示
- 🔄 实时数据更新和验证

## 📁 项目结构

```
src/
├── app/                      # Next.js App Router
│   ├── (dashboard)/
│   │   └── page.tsx          # 主仪表板页面
│   ├── api/                  # API 路由
│   │   ├── daycare-data-2023/ # 2023年数据API
│   │   └── zip-boundaries/   # ZIP边界数据API
│   ├── globals.css           # 全局样式
│   └── layout.tsx            # 根布局组件
├── components/               # React 组件
│   ├── map/                  # 地图相关组件
│   │   ├── MapContainer.tsx  # 主地图容器
│   │   ├── SearchBox.tsx     # 地址搜索框
│   │   ├── MapLegend.tsx     # 地图图例
│   │   ├── MapErrorBoundary.tsx # 错误边界
│   │   └── index.tsx         # 地图组件入口
│   ├── ui/                   # 通用UI组件
│   ├── LanguageToggle.tsx    # 语言切换器
│   ├── SimpleThemeToggle.tsx # 主题切换器
│   └── ErrorBoundary.tsx     # 全局错误边界
├── hooks/                    # 自定义 Hooks
│   ├── useDaycareMap.ts      # 地图数据管理
│   ├── useMapLanguage.ts     # 地图多语言
│   ├── useLanguage.ts        # 全局语言管理
│   └── useTheme.ts           # 主题管理
├── lib/                      # 工具库
│   ├── i18n.ts               # 国际化配置
│   ├── api.ts                # API 工具
│   └── utils.ts              # 通用工具函数
├── locales/                  # 多语言文件
│   ├── zh-CN/                # 中文翻译
│   └── en-US/                # 英文翻译
├── provider/                 # Context Providers
│   ├── I18nProvider.tsx      # 国际化提供者
│   ├── ThemeProvider.tsx     # 主题提供者
│   └── RoutePerformanceProvider.tsx # 性能监控
├── store/                    # 状态管理
│   ├── useAppStore.ts        # 应用状态
│   └── useConfig.ts          # 配置状态
├── types/                    # TypeScript 类型定义
│   ├── daycare.ts            # 托儿所数据类型
│   └── index.ts              # 通用类型
├── utils/                    # 工具函数
│   ├── zipCodeCoordinates.ts # ZIP坐标处理
│   └── validation.ts         # 数据验证
└── config/                   # 配置文件
    └── config.ts             # 应用配置

data/                         # 数据文件
├── processed_2023/           # 2023年处理后数据
│   ├── daycare_data_2023.geojson # GeoJSON格式
│   ├── api_data_2023.json    # API数据
│   └── stats_2023.json       # 统计信息
├── zip_code_saturation.csv   # 原始CSV数据
├── create_2023_data.js       # 数据处理脚本
└── README.md                 # 数据文档

public/                       # 静态资源
├── data/                     # 公共数据文件
└── *.svg                     # 图标文件
```

## 📊 数据结构

### 2023 年数据格式

应用使用基于 GeoJSON 标准的数据格式：

```typescript
interface DaycareFeatureProperties {
  zip_code: string; // ZIP 码
  total_births: number; // 总出生数
  total_capacity: number; // 总托儿所容量
  saturation: number; // 饱和度比率
  saturation_level: SaturationLevel; // 饱和度等级
  saturation_level_text: string; // 等级描述文本
  year: number; // 数据年份 (2023)
}

type SaturationLevel = "low" | "medium" | "high" | "very-high";
```

### 饱和度等级分类

- **低饱和度 (low)**: ≤ 0.5 - 市场机会充足
- **中等饱和度 (medium)**: 0.5 - 1.0 - 适度竞争
- **高饱和度 (high)**: 1.0 - 2.0 - 高度竞争
- **极高饱和度 (very-high)**: > 2.0 - 市场过饱和

### 数据统计

- **总 ZIP 码数量**: 1,269 个
- **饱和度范围**: 0.11 - 49.21
- **平均饱和度**: 3.26
- **总出生数**: 395,152
- **总容量**: 1,034,262

## 🔌 API 接口

### GET `/api/daycare-data-2023`

返回 2023 年所有托儿所数据记录。

**查询参数**:

- `format=geojson`: 返回 GeoJSON 格式数据
- `format=json`: 返回简化 JSON 格式数据

### GET `/api/daycare-data-2023?stats=true`

返回数据集统计信息，包括：

- 总记录数
- 饱和度分布
- 地理范围
- 数据质量指标

### GET `/api/zip-boundaries`

返回 ZIP 码边界数据，用于地图可视化。

## ⚙️ 配置说明

### 环境变量

```bash
# 必需: Mapbox 访问令牌
NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN=your_token_here

# 可选: 自定义配置
NEXT_PUBLIC_LOG_LEVEL=info
NEXT_PUBLIC_ENABLE_CACHE=true
```

### Mapbox 地图配置

应用针对加州地区进行了优化配置：

- **中心点**: [-119.4179, 36.7783] (加州地理中心)
- **缩放级别**: 7 (州级视图)
- **边界限制**: 限制在加州坐标范围内
- **地图样式**: 浅色主题，便于数据可视化
- **控件**: 导航控件、比例尺、全屏控件

### 多语言配置

- **支持语言**: 中文 (zh-CN)、英文 (en-US)
- **默认语言**: 中文
- **存储方式**: localStorage 持久化
- **回退机制**: 多层翻译回退策略

### 主题配置

- **支持主题**: 明亮模式、暗黑模式
- **默认主题**: 暗黑模式
- **自动检测**: 支持系统主题偏好
- **平滑过渡**: 主题切换动画效果

## 🔍 数据验证

应用包含全面的数据验证机制：

- **必填字段**: ZIP 码、容量、出生数、饱和度
- **数据类型**: 所有指标的数值验证
- **地理边界**: 加州坐标范围验证
- **交叉验证**: 饱和度计算验证
- **重复检测**: ZIP 码唯一性检查
- **数据完整性**: GeoJSON 格式验证

## 🛡️ 错误处理

- **地图错误**: 错误边界和重试机制
- **API 失败**: 用户友好的错误消息
- **网络问题**: 离线友好的设计模式
- **数据加载**: 优雅的加载状态和错误回退
- **地理编码失败**: 坐标生成回退机制

## 🔧 故障排除

### 常见问题

1. **地图无法加载**:

   - 检查 `.env.local` 中的 Mapbox 令牌
   - 查看浏览器控制台错误信息
   - 确认令牌具有正确权限

2. **数据不显示**:

   - 运行 `node data/create_2023_data.js` 生成数据
   - 检查 API 端点 `/api/daycare-data-2023`
   - 验证 `data/processed_2023/` 目录存在

3. **搜索功能不工作**:

   - 确认 Mapbox 令牌包含地理编码权限
   - 检查网络连接
   - 验证 API 速率限制

4. **构建错误**:

   - 清理 `.next` 目录: `rm -rf .next`
   - 重新安装依赖: `rm -rf node_modules && npm install`
   - 检查 TypeScript 错误: `npm run build`

5. **多语言不生效**:

   - 检查浏览器 localStorage 中的语言设置
   - 确认翻译文件存在于 `src/locales/` 目录
   - 查看控制台是否有翻译错误

6. **主题切换问题**:
   - 清理浏览器缓存和 localStorage
   - 检查 CSS 变量是否正确加载
   - 确认主题提供者正确包装应用
