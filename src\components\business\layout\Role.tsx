"use client";

import * as React from "react";
import { SidebarMenu, SidebarMenuItem } from "@/components/ui/sidebar";
import { useSidebar } from "@/components/ui/sidebar";

export function TeamSwitcher() {
  const { state } = useSidebar();

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <div className="app-logo">
          <div className="logo-container">
            <div className="persona-icon">
              <div className="persona-layers">
                <div className="persona-layer layer-1"></div>
                <div className="persona-layer layer-2"></div>
                <div className="persona-layer layer-3"></div>
              </div>
            </div>
            {state === "collapsed" ? null : (
              <span className="logo-text">MAP</span>
            )}
          </div>
        </div>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
