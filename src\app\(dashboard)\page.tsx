"use client";

import React, { useMemo } from "react";
import DaycareMap from "@/components/map";
import { PreloadWrapper } from "@/components/PreloadWrapper";
import { useMapLanguage } from "@/hooks/useMapLanguage";
import { useAppStore } from "@/store/useAppStore";

/**
 * 主仪表板页面 - 展示加州托儿所市场饱和度地图
 */
export default function HomePage() {
  const { t } = useMapLanguage();
  const { setHeaderSlot } = useAppStore();

  // 使用 useMemo 来稳定 header slot 的内容
  const headerSlot = useMemo(
    () => (
      <div className="w-full flex items-center justify-between">
        <div className="text-2xl font-bold">{t("map:page.title")}</div>
        <div className="flex items-center space-x-4">
          <div className="text-sm">
            {t("map:page.dataYear")} <span className="font-medium">2023</span>
          </div>
        </div>
      </div>
    ),
    [t]
  );

  React.useEffect(() => {
    setHeaderSlot(headerSlot);
  }, [headerSlot, setHeaderSlot]);
  return (
    <PreloadWrapper>
      <div className="h-[calc(100vh-100px)] w-full flex flex-col">
        <main className="flex-1 relative">
          <DaycareMap />
        </main>
      </div>
    </PreloadWrapper>
  );
}
