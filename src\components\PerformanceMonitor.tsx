"use client";

import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";
import { useAppStore } from "@/store/useAppStore";

interface PerformanceMetrics {
  route: string;
  loadTime: number;
  renderTime: number;
  timestamp: number;
}

export const PerformanceMonitor = () => {
  const pathname = usePathname();
  const { recordRouteLoadTime } = useAppStore();
  const [metrics, setMetrics] = useState<PerformanceMetrics[]>([]);

  useEffect(() => {
    const startTime = performance.now();

    // 记录路由开始时间
    performance.mark(`route-${pathname}-start`);

    // 在下一个事件循环中测量渲染时间
    const measureRenderTime = () => {
      const endTime = performance.now();
      const renderTime = endTime - startTime;

      // 记录路由结束时间
      performance.mark(`route-${pathname}-end`);

      try {
        // 测量总时间
        performance.measure(
          `route-${pathname}-duration`,
          `route-${pathname}-start`,
          `route-${pathname}-end`
        );

        const measure = performance.getEntriesByName(
          `route-${pathname}-duration`
        )[0];
        const loadTime = measure ? measure.duration : renderTime;

        // 记录到 store
        recordRouteLoadTime(pathname ?? "", loadTime);

        // 记录到本地状态
        const newMetric: PerformanceMetrics = {
          route: pathname ?? "",
          loadTime,
          renderTime,
          timestamp: Date.now(),
        };

        setMetrics((prev) => [...prev.slice(-9), newMetric]); // 保留最近10条记录

        // 开发环境下的性能警告
        if (process.env.NODE_ENV === "development") {
          if (loadTime > 500) {
            console.warn(
              `🐌 Slow route detected: ${pathname} took ${loadTime.toFixed(
                2
              )}ms`
            );
          } else if (loadTime > 200) {
            console.log(`⚠️ Route ${pathname} took ${loadTime.toFixed(2)}ms`);
          } else {
            console.log(
              `✅ Route ${pathname} loaded in ${loadTime.toFixed(2)}ms`
            );
          }
        }
      } catch (error) {
        console.error("Performance measurement error:", error);
      }
    };

    // 使用 requestIdleCallback 或 setTimeout 来延迟测量
    if ("requestIdleCallback" in window) {
      requestIdleCallback(measureRenderTime);
    } else {
      setTimeout(measureRenderTime, 0);
    }

    return () => {
      // 清理性能标记
      try {
        performance.clearMarks(`route-${pathname}-start`);
        performance.clearMarks(`route-${pathname}-end`);
        performance.clearMeasures(`route-${pathname}-duration`);
      } catch (error) {
        console.error("Performance API cleanup failed:", error);
        // 忽略清理错误
      }
    };
  }, [pathname, recordRouteLoadTime]);

  // 开发环境下显示性能面板
  if (process.env.NODE_ENV === "development") {
    return (
      <div className="fixed bottom-4 right-4 bg-black/80 text-white p-3 rounded-lg text-xs font-mono z-50 max-w-xs">
        <div className="font-bold mb-2">Performance Monitor</div>
        <div className="space-y-1">
          <div>Current: {pathname}</div>
          {metrics.slice(-3).map((metric, index) => (
            <div
              key={index}
              className={`${
                metric.loadTime > 300
                  ? "text-red-400"
                  : metric.loadTime > 150
                  ? "text-yellow-400"
                  : "text-green-400"
              }`}
            >
              {metric.route}: {metric.loadTime.toFixed(0)}ms
            </div>
          ))}
        </div>
        <div className="mt-2 text-gray-400">Press Ctrl+Shift+P to toggle</div>
      </div>
    );
  }

  return null;
};
