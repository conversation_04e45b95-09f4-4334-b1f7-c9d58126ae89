"use client";

import { useTranslation } from "react-i18next";
import { SUPPORTED_LANGUAGES } from "@/lib/i18n";
import useConfigStore from "@/store/useConfig";

export function useLanguage() {
  // 加载所有命名空间，让 t 函数可以访问所有翻译
  const { t } = useTranslation();
  const { language: storeLanguage, setLanguage: setStoreLanguage } =
    useConfigStore();

  // 使用 store 中的语言作为当前语言
  const currentLanguage = storeLanguage as SUPPORTED_LANGUAGES;

  // 更新语言时同时更新 store
  const changeLanguage = (language: SUPPORTED_LANGUAGES) => {
    setStoreLanguage(language as SUPPORTED_LANGUAGES);
  };

  return {
    currentLanguage,
    changeLanguage,
    t,
  };
}
