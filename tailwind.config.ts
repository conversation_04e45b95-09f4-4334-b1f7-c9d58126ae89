import tailwindcssForms from "@tailwindcss/forms";
import tailwindcssLineClamp from "@tailwindcss/line-clamp";
import type { Config } from "tailwindcss";
import tailwindcssAnimate from "tailwindcss-animate";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/pages-components/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  darkMode: ["class", '[data-theme="dark"]'], // 支持手动主题切换
  prefix: "",
  theme: {
    extend: {
      colors: {
        // 主色调系统 - 完全使用 CSS 变量
        primary: {
          50: "var(--primary-50, #fef2f2)",
          100: "var(--primary-100, #fee2e2)",
          200: "var(--primary-200, #fecaca)",
          300: "var(--primary-300, #fca5a5)",
          400: "var(--primary-400, #f87171)",
          500: "var(--primary)",
          600: "var(--primary-dark)",
          700: "var(--primary-700, #dc2626)",
          800: "var(--primary-800, #b91c1c)",
          900: "var(--primary-900, #991b1b)",
          DEFAULT: "var(--primary)",
          dark: "var(--primary-dark)",
          light: "var(--primary-light)",
        },
        secondary: {
          50: "var(--secondary-50, #fffbeb)",
          100: "var(--secondary-100, #fef3c7)",
          200: "var(--secondary-200, #fde68a)",
          300: "var(--secondary-300, #fcd34d)",
          400: "var(--secondary-400, #fbbf24)",
          500: "var(--secondary)",
          600: "var(--secondary-600, #d97706)",
          700: "var(--secondary-700, #b45309)",
          800: "var(--secondary-800, #92400e)",
          900: "var(--secondary-900, #78350f)",
          DEFAULT: "var(--secondary)",
        },

        // 强调色 - 使用 CSS 变量
        accent: {
          50: "var(--accent-50, #ecfeff)",
          100: "var(--accent-100, #cffafe)",
          200: "var(--accent-200, #a5f3fc)",
          300: "var(--accent-300, #67e8f9)",
          400: "var(--accent-400, #22d3ee)",
          500: "var(--accent)",
          600: "var(--accent-600, #0891b2)",
          700: "var(--accent-700, #0e7490)",
          800: "var(--accent-800, #155e75)",
          900: "var(--accent-900, #164e63)",
          DEFAULT: "var(--accent)",
        },

        // 背景色系统 - 使用 CSS 变量
        background: "var(--background)",
        surface: {
          DEFAULT: "var(--surface)",
          light: "var(--surface-light)",
          lighter: "var(--surface-lighter)",
        },
        border: {
          DEFAULT: "var(--border)",
          light: "var(--border-light)",
        },

        // 文本色系统 - 使用 CSS 变量
        text: {
          primary: "var(--text-primary)",
          secondary: "var(--text-secondary)",
          tertiary: "var(--text-tertiary)",
        },

        // 状态色系统 - 使用 CSS 变量
        success: "var(--success)",
        warning: "var(--warning)",
        error: "var(--error)",
        info: "var(--info)",

        // 透明度色值 - 使用 CSS 变量
        "primary-alpha": {
          5: "var(--primary-alpha-05)",
          10: "var(--primary-alpha-10)",
          20: "var(--primary-alpha-20)",
          30: "var(--primary-alpha-30)",
        },
        "success-alpha": {
          5: "var(--success-alpha-05)",
          10: "var(--success-alpha-10)",
        },
        "warning-alpha": {
          10: "var(--warning-alpha-10)",
        },
        "error-alpha": {
          10: "var(--error-alpha-10)",
        },
        "info-alpha": {
          10: "var(--info-alpha-10)",
        },
        "white-alpha": {
          5: "var(--white-alpha-05)",
          10: "var(--white-alpha-10)",
          20: "var(--white-alpha-20)",
        },
        "black-alpha": {
          80: "var(--black-alpha-80)",
        },

        // 特殊色值
        brown: "var(--brown-color)",
        "brown-alpha": {
          10: "var(--brown-alpha-10)",
        },

        // 兼容性
        foreground: "var(--foreground)",

        // ===== UI 库适配变量 =====
        // 基础 UI 变量
        card: {
          DEFAULT: "var(--card)",
          foreground: "var(--card-foreground)",
        },
        popover: {
          DEFAULT: "var(--popover)",
          foreground: "var(--popover-foreground)",
        },
        muted: {
          DEFAULT: "var(--muted)",
          foreground: "var(--muted-foreground)",
        },
        destructive: "var(--destructive)",
        input: "var(--input)",
        ring: "var(--ring)",

        // Sidebar 变量
        sidebar: {
          DEFAULT: "var(--sidebar)",
          foreground: "var(--sidebar-foreground)",
          primary: "var(--sidebar-primary)",
          "primary-foreground": "var(--sidebar-primary-foreground)",
          accent: "var(--sidebar-accent)",
          "accent-foreground": "var(--sidebar-accent-foreground)",
          border: "var(--sidebar-border)",
          ring: "var(--sidebar-ring)",
        },

        // 其他 UI 变量
        selection: {
          DEFAULT: "var(--selection)",
          foreground: "var(--selection-foreground)",
        },
        code: {
          DEFAULT: "var(--code)",
          foreground: "var(--code-foreground)",
          highlight: "var(--code-highlight)",
          number: "var(--code-number)",
        },
      },
      backgroundImage: {
        "gradient-1": "var(--gradient-1)",
        "gradient-2": "var(--gradient-2)",
        "gradient-primary": "var(--gradient-primary)",
        "gradient-primary-light": "var(--gradient-primary-light)",
        "gradient-primary-dark": "var(--gradient-primary-dark)",
      },
      boxShadow: {
        sm: "var(--shadow-sm)",
        md: "var(--shadow-md)",
        lg: "var(--shadow-lg)",
        glow: "var(--shadow-glow)",
        "glow-lg": "var(--shadow-glow-lg)",
      },
      animation: {
        float: "float 20s ease-in-out infinite",
        "pulse-dot": "pulse-dot 1.4s ease-in-out infinite",
        loading: "loading 1.5s ease-in-out infinite",
      },
      keyframes: {
        float: {
          "0%, 100%": { transform: "translate(0, 0) rotate(0deg)" },
          "33%": { transform: "translate(-20px, -20px) rotate(1deg)" },
          "66%": { transform: "translate(20px, -10px) rotate(-1deg)" },
        },
        "pulse-dot": {
          "0%, 80%, 100%": { transform: "scale(0)", opacity: "0" },
          "40%": { transform: "scale(1)", opacity: "1" },
        },
        loading: {
          "0%": { "background-position": "200% 0" },
          "100%": { "background-position": "-200% 0" },
        },
      },
      fontFamily: {
        sans: [
          "-apple-system",
          "BlinkMacSystemFont",
          "Inter",
          "var(--font-geist-sans)",
          "sans-serif",
        ],
        mono: ["var(--font-geist-mono)", "monospace"],
      },
      backdropBlur: {
        xs: "2px",
      },
      borderRadius: {
        "4xl": "2rem",
        // UI 库圆角变量
        sm: "calc(var(--radius) - 4px)",
        md: "calc(var(--radius) - 2px)",
        lg: "var(--radius)",
        xl: "calc(var(--radius) + 4px)",
      },
    },
  },
  plugins: [
    tailwindcssAnimate, // Tailwind 插件，tailwind 预置动画
    tailwindcssForms, // Tailwind 插件，改进原生 HTML 表单元素的样式, 与 Tailwind 的设计风格保持一致
    tailwindcssLineClamp, // Tailwind 插件，提供 line-clamp 功能
  ],
};

export default config;
