/**
 * 托儿所数据类型定义
 */

// 饱和度等级
export type SaturationLevel = 'low' | 'medium' | 'high' | 'very-high';

// 基础托儿所数据
export interface DaycareData {
  zip_code: string;
  total_births: number;
  total_capacity: number;
  saturation: number;
  saturation_level: SaturationLevel;
  latitude: number;
  longitude: number;
}

// 扩展的托儿所数据（包含年份信息）
export interface ExtendedDaycareData extends DaycareData {
  saturation_level_text: string;
  year: number;
}

// GeoJSON Feature 属性
export interface DaycareFeatureProperties {
  zip_code: string;
  total_births: number;
  total_capacity: number;
  saturation: number;
  saturation_level: SaturationLevel;
  saturation_level_text: string;
  year: number;
}

// GeoJSON Feature
export interface DaycareFeature {
  type: 'Feature';
  geometry: {
    type: 'Point';
    coordinates: [number, number]; // [longitude, latitude]
  };
  properties: DaycareFeatureProperties;
}

// GeoJSON FeatureCollection
export interface DaycareGeoJSON {
  type: 'FeatureCollection';
  metadata: {
    title: string;
    description: string;
    year: number;
    total_features: number;
    generated_at: string;
  };
  features: DaycareFeature[];
}

// 统计信息
export interface DaycareStats {
  year: number;
  total_zip_codes: number;
  saturation: {
    min: number;
    max: number;
    avg: number;
  };
  births: {
    min: number;
    max: number;
    avg: number;
    total: number;
  };
  capacity: {
    min: number;
    max: number;
    avg: number;
    total: number;
  };
  level_distribution: {
    low: number;
    medium: number;
    high: number;
    very_high: number;
  };
  generated_at: string;
}

// 地图配置
export interface MapConfig {
  center: [number, number];
  zoom: number;
  bounds: [[number, number], [number, number]];
  style: string;
}

// 搜索结果
export interface SearchResult {
  id: string;
  place_name: string;
  center: [number, number];
  bbox?: [number, number, number, number];
  context?: Array<{
    id: string;
    text: string;
  }>;
}

// 地图事件
export interface MapClickEvent {
  lngLat: {
    lng: number;
    lat: number;
  };
  features: DaycareFeature[];
}

// 弹窗数据
export interface PopupData {
  zip_code: string;
  total_births: number;
  total_capacity: number;
  saturation: number;
  saturation_level: SaturationLevel;
  saturation_level_text: string;
  coordinates: [number, number];
}

// 图例项
export interface LegendItem {
  level: SaturationLevel;
  color: string;
  label: string;
  count: number;
  range: string;
}

// 地图状态
export interface MapState {
  isLoaded: boolean;
  isLoading: boolean;
  error: string | null;
  data: DaycareData[] | null;
  stats: DaycareStats | null;
  selectedZip: string | null;
  searchQuery: string;
  searchResults: SearchResult[];
}

// API 响应
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  error?: string;
  timestamp: string;
}

// 环境变量
export interface MapboxEnv {
  NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN: string;
}

// 颜色配置
export interface ColorConfig {
  low: string;
  medium: string;
  high: string;
  'very-high': string;
}

// 大小配置
export interface SizeConfig {
  min: number;
  max: number;
  multiplier: number;
}

// 地图样式配置
export interface MapStyleConfig {
  colors: ColorConfig;
  sizes: SizeConfig;
  opacity: number;
  strokeWidth: number;
  strokeColor: string;
}

// 组件 Props
export interface MapContainerProps {
  className?: string;
  height?: string | number;
  onMapLoad?: () => void;
  onError?: (error: Error) => void;
}

export interface SearchBoxProps {
  onSearch?: (query: string) => void;
  onResultSelect?: (result: SearchResult) => void;
  placeholder?: string;
  className?: string;
}

export interface MapLegendProps {
  stats: DaycareStats;
  className?: string;
}

export interface MarkerPopupProps {
  data: PopupData;
  onClose?: () => void;
}

// 常量
export const SATURATION_LEVELS: Record<SaturationLevel, { label: string; range: string }> = {
  low: { label: '低饱和度', range: '≤0.5' },
  medium: { label: '中等饱和度', range: '0.5-1.0' },
  high: { label: '高饱和度', range: '1.0-2.0' },
  'very-high': { label: '极高饱和度', range: '>2.0' }
};

export const DEFAULT_COLORS: ColorConfig = {
  low: '#22c55e',
  medium: '#eab308',
  high: '#f97316',
  'very-high': '#ef4444'
};

export const CALIFORNIA_BOUNDS: [[number, number], [number, number]] = [
  [-124.4, 32.5], // Southwest
  [-114.1, 42.0]  // Northeast
];

export const CALIFORNIA_CENTER: [number, number] = [-119.4179, 36.7783];
