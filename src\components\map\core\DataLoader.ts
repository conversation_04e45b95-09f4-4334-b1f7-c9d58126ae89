import type { DaycareGeoJSON } from "@/types/daycare";
import {
  extractZipCoordinates,
  mergeZipBoundariesWithPropertiesData,
} from "@/utils/zipCodeCoordinates";
import { LocalDataLoader } from "../data/local-data-manager";

/**
 * 数据加载管理器
 * 负责处理托儿所数据和ZIP边界数据的加载逻辑
 * 现在使用本地数据管理器，避免API请求
 */
export class DataLoader {
  private localDataLoader: LocalDataLoader;
  private zipBoundariesData: any = null;
  private zipCoordinates: Map<string, [number, number]> = new Map();
  private isZipDataLoaded: boolean = false;

  constructor() {
    this.localDataLoader = new LocalDataLoader();
  }

  /**
   * 预加载ZIP边界数据（使用本地数据）
   */
  async preloadZipBoundaries(): Promise<any> {
    if (this.isZipDataLoaded) {
      return this.zipBoundariesData;
    }

    try {
      console.log("🔄 从本地加载ZIP边界数据...");

      // 使用本地数据管理器
      const data = await this.localDataLoader.preloadZipBoundaries();

      if (!data) {
        console.warn("⚠️ ZIP边界数据为空");
        return null;
      }

      this.zipBoundariesData = data;

      // 提取ZIP坐标映射
      const coordinates = extractZipCoordinates(data);
      this.zipCoordinates = coordinates;

      this.isZipDataLoaded = true;
      console.log(
        "✅ ZIP边界数据加载完成:",
        data.features?.length || 0,
        "个区域"
      );
      return data;
    } catch (error) {
      console.error("❌ ZIP边界数据加载失败:", error);
      return null;
    }
  }

  /**
   * 加载托儿所数据并与ZIP边界合并（使用本地数据）
   */
  async loadDaycareData(zipData?: any): Promise<any> {
    try {
      console.log("🔄 从本地加载托儿所数据...");

      // 使用本地数据管理器加载数据
      const result = await this.localDataLoader.loadDaycareData(zipData);

      console.log("✅ 托儿所数据加载完成:", result.type);
      return result;
    } catch (error) {
      console.error("❌ 托儿所数据加载失败:", error);
      // 回退到点数据
      return await this.loadPointData();
    }
  }

  /**
   * 加载点数据（回退方案，使用本地数据）
   */
  async loadPointData(): Promise<{ type: string; data: DaycareGeoJSON }> {
    try {
      return await this.localDataLoader.loadPointData();
    } catch (error) {
      console.error("❌ 点数据加载失败:", error);
      throw error;
    }
  }

  /**
   * 获取ZIP边界数据
   */
  getZipBoundariesData(): any {
    return this.zipBoundariesData;
  }

  /**
   * 获取ZIP坐标映射
   */
  getZipCoordinates(): Map<string, [number, number]> {
    return this.zipCoordinates;
  }

  /**
   * 检查ZIP数据是否已加载
   */
  isZipDataReady(): boolean {
    return this.isZipDataLoaded;
  }

  /**
   * 重置数据状态
   */
  reset(): void {
    this.zipBoundariesData = null;
    this.zipCoordinates = new Map();
    this.isZipDataLoaded = false;
  }

  /**
   * 设置ZIP边界数据
   */
  setZipBoundariesData(data: any): void {
    this.zipBoundariesData = data;
    if (data) {
      this.zipCoordinates = extractZipCoordinates(data);
      this.isZipDataLoaded = true;
    }
  }
}
