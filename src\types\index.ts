import { JSX } from "react";

// 主题相关类型
export type Theme = "light" | "dark" | "system";
export type ResolvedTheme = "light" | "dark";

// 语言相关类型

// 组件通用类型
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

// 文本组件类型
export enum TextVariant {
  H1 = "h1",
  H2 = "h2",
  H3 = "h3",
  H4 = "h4",
  H5 = "h5",
  H6 = "h6",
  TITLE_LARGE = "title-large",
  TITLE_MEDIUM = "title-medium",
  TITLE_SMALL = "title-small",
  BODY_LARGE = "body-large",
  BODY_MEDIUM = "body-medium",
  BODY_SMALL = "body-small",
  CAPTION = "caption",
}

export interface TextProps extends BaseComponentProps {
  variant?: TextVariant;
  as?: keyof JSX.IntrinsicElements;
  onClick?: () => void;
  dangerouslySetInnerHTML?: { __html: string };
}

// 按钮组件类型
export type ButtonVariant =
  | "primary"
  | "secondary"
  | "outline"
  | "ghost"
  | "danger";
export type ButtonSize = "sm" | "md" | "lg";

export interface ButtonProps extends BaseComponentProps {
  variant?: ButtonVariant;
  size?: ButtonSize;
  disabled?: boolean;
  loading?: boolean;
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  type?: "button" | "submit" | "reset";
}

// 卡片组件类型
export interface CardProps extends BaseComponentProps {
  variant?: "default" | "glass" | "elevated";
  padding?: "none" | "sm" | "md" | "lg";
  hoverable?: boolean;
}

// API 响应类型
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: any;
  meta: {
    requestId: string;
    timestamp: string;
    version: string;
  };
}

// 错误类型
export interface AppError {
  code: string;
  message: string;
  details?: unknown;
}

// 加载状态类型
export type LoadingState = "idle" | "loading" | "success" | "error";

// 表单相关类型
export interface FormFieldProps {
  name: string;
  label?: string;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  error?: string;
  helperText?: string;
}

// 导航相关类型
export interface NavItem {
  key: string;
  label: string;
  href: string;
  icon?: React.ComponentType;
  children?: NavItem[];
}

// 分页类型
export interface Pagination {
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: Pagination;
}

// 搜索和筛选类型
export interface SearchParams {
  query?: string;
  filters?: Record<string, unknown>;
  sort?: {
    field: string;
    order: "asc" | "desc";
  };
  pagination?: Pick<Pagination, "page" | "pageSize">;
}

// 通知类型
export type NotificationType = "success" | "error" | "warning" | "info";

export interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message?: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}

// 模态框类型
export interface ModalProps extends BaseComponentProps {
  open: boolean;
  onClose: () => void;
  title?: string;
  size?: "sm" | "md" | "lg" | "xl" | "full";
  closable?: boolean;
  maskClosable?: boolean;
}

// 工具类型
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredKeys<T, K extends keyof T> = T & Required<Pick<T, K>>;
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};
