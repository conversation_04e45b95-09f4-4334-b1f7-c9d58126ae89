import { toast } from "sonner";
import { API_CONFIG, CACHE_CONFIG, ERROR_MESSAGES } from "@/config";
import i18n from "@/lib/i18n";
import type { ApiResponse } from "@/types";

// API 错误类
export class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public code?: string,
    public details?: unknown
  ) {
    super(message);
    this.name = "ApiError";
  }
}

// 错误拦截配置接口
interface ErrorInterceptConfig {
  showToast?: boolean; // 是否显示 Toast，默认 true
  toastType?: "error" | "warning" | "info"; // Toast 类型，默认 'error'
  customMessage?: string; // 自定义错误消息
  silent?: boolean; // 静默模式，不显示任何提示，默认 false
  onError?: (error: ApiError) => void; // 自定义错误处理回调
}

// 请求配置接口
interface RequestConfig extends RequestInit {
  timeout?: number;
  retries?: number;
  retryDelay?: number;
  errorConfig?: ErrorInterceptConfig; // 错误拦截配置
}

// 创建基础 fetch 函数
async function baseFetch(
  url: string,
  config: RequestConfig = {}
): Promise<Response> {
  const {
    timeout = API_CONFIG.timeout,
    retries = 3,
    retryDelay = 1000,
    ...fetchConfig
  } = config;
  // 设置默认请求头
  const defaultHeaders = {
    "Content-Type": "application/json",
    Authorization: `Bearer ${localStorage.getItem("token")}`,
    ...fetchConfig.headers,
  };

  // 创建 AbortController 用于超时控制
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  const fetchWithRetry = async (attempt: number): Promise<Response> => {
    try {
      const response = await fetch(url, {
        ...fetchConfig,
        headers: defaultHeaders,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);
      if (!response.ok) {
        // 在抛出错误前，尝试读取响应体以获取更多信息
        let errorBody = "";
        try {
          errorBody = await response.text();
        } catch (readError) {
          console.error("Failed to read error response:", readError);
        }

        throw new ApiError(
          `HTTP ${response.status}: ${response.statusText}. Body: ${errorBody}`,
          response.status
        );
      }

      return response;
    } catch (error) {
      clearTimeout(timeoutId);
      if (attempt < retries && !(error instanceof ApiError)) {
        await new Promise((resolve) => setTimeout(resolve, retryDelay));
        return fetchWithRetry(attempt + 1);
      }

      throw error;
    }
  };

  return fetchWithRetry(1);
}

// 基础 URL 配置
const baseURL = API_CONFIG.baseURL.replace(/\/$/, ""); // 移除末尾斜杠

// 获取完整 URL
function getFullUrl(endpoint: string): string {
  const cleanEndpoint = endpoint.startsWith("/") ? endpoint : `/${endpoint}`;
  return `${baseURL}${cleanEndpoint}`;
}

// 根据错误状态码获取错误消息
function getErrorMessage(error: ApiError): string {
  // 如果 i18n 已初始化，使用多语言消息，否则使用默认消息
  if (i18n.isInitialized) {
    switch (error.status) {
      case 400:
        return i18n.t("message:error.validation");
      case 401:
      case 403:
        localStorage.clear();
        return i18n.t("message:error.forbidden");
      case 404:
        return i18n.t("message:error.notFound");
      case 408:
        return i18n.t("message:error.timeout");
      case 500:
      case 502:
      case 503:
      case 504:
        return i18n.t("message:error.server");
      default:
        if (error.message.includes("fetch")) {
          return i18n.t("message:error.network");
        }
        return error.message || i18n.t("message:error.server");
    }
  }

  // 降级到默认消息（当 i18n 未初始化时）
  switch (error.status) {
    case 400:
      return ERROR_MESSAGES.VALIDATION_ERROR;
    case 401:
    case 403:
      window.location.href = "/login";
      localStorage.clear();
      return ERROR_MESSAGES.UNAUTHORIZED;
    case 404:
      return ERROR_MESSAGES.NOT_FOUND;
    case 408:
      return ERROR_MESSAGES.TIMEOUT_ERROR;
    case 500:
    case 502:
    case 503:
    case 504:
      return ERROR_MESSAGES.SERVER_ERROR;
    default:
      if (error.message.includes("fetch")) {
        return ERROR_MESSAGES.NETWORK_ERROR;
      }
      return error.message || ERROR_MESSAGES.SERVER_ERROR;
  }
}

// 错误拦截处理函数
function handleApiErrorIntercept(
  error: ApiError,
  config?: ErrorInterceptConfig
): void {
  const {
    showToast = true,
    toastType = "error",
    customMessage,
    silent = false,
    onError,
  } = config || {};

  // 静默模式，不做任何处理
  if (silent) {
    return;
  }

  // 自定义错误处理优先
  if (onError) {
    onError(error);
    return;
  }

  // 显示 Toast 通知
  if (showToast) {
    const message = customMessage || getErrorMessage(error);

    // 获取多语言错误描述
    const getErrorDescription = () => {
      if (!error.code) {
        return undefined;
      }

      if (i18n.isInitialized) {
        const errorCodeLabel = i18n.t("message:error.code", {
          defaultValue: "错误代码",
        });
        return `${errorCodeLabel}: ${error.code}`;
      }

      return `错误代码: ${error.code}`;
    };

    switch (toastType) {
      case "error":
        toast.error(message, {
          duration: 4000,
          description: getErrorDescription(),
        });
        break;
      case "warning":
        toast.warning(message, {
          duration: 3000,
        });
        break;
      case "info":
        toast.info(message, {
          duration: 3000,
        });
        break;
    }
  }
}

// 处理响应
async function handleResponse<T>(
  response: Response,
  errorConfig?: ErrorInterceptConfig
): Promise<ApiResponse<T>> {
  try {
    const responseText = await response.text();

    let data;
    try {
      data = JSON.parse(responseText);
    } catch (parseError) {
      throw new ApiError(
        `Invalid JSON response: ${responseText}`,
        response.status,
        "INVALID_JSON",
        parseError
      );
    }

    return data;
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }

    const apiError = new ApiError(
      "响应解析失败",
      response.status,
      "PARSE_ERROR",
      error
    );

    // 触发错误拦截
    handleApiErrorIntercept(apiError, errorConfig);
    throw apiError;
  }
}

// 基础 POST 请求
async function basePost<T = unknown>(
  endpoint: string,
  data?: unknown,
  config?: RequestConfig
): Promise<ApiResponse<T>> {
  try {
    const fullUrl = getFullUrl(endpoint);
    const requestBody = data ? JSON.stringify(data) : undefined;

    const response = await baseFetch(fullUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        ...config?.headers,
      },
      body: requestBody,
      ...config,
    });

    return handleResponse<T>(response, config?.errorConfig);
  } catch (error) {
    console.error("API Request Failed:", {
      endpoint,
      data,
      error,
      errorMessage: error instanceof Error ? error.message : "Unknown error",
    });

    if (error instanceof ApiError) {
      // 如果是网络错误或其他非业务错误，也触发错误拦截
      handleApiErrorIntercept(error, config?.errorConfig);
    }
    throw error;
  }
}

// 基础上传文件
async function baseUpload<T = unknown>(
  endpoint: string,
  file: File | FormData,
  config?: Omit<RequestConfig, "headers">
): Promise<ApiResponse<T>> {
  try {
    let body: FormData;

    if (file instanceof FormData) {
      body = file;
    } else {
      body = new FormData();
      body.append("file", file);
    }

    const response = await baseFetch(getFullUrl(endpoint), {
      method: "POST",
      body,
      ...config,
      headers: {
        // 不设置 Content-Type，让浏览器自动设置
      },
    });

    return handleResponse<T>(response, config?.errorConfig);
  } catch (error) {
    if (error instanceof ApiError) {
      handleApiErrorIntercept(error, config?.errorConfig);
    }
    throw error;
  }
}

// 基础 GET 请求
async function baseGet<T = unknown>(
  endpoint: string,
  config?: RequestConfig
): Promise<ApiResponse<T>> {
  try {
    const response = await baseFetch(getFullUrl(endpoint), {
      method: "GET",
      ...config,
    });

    return handleResponse<T>(response, config?.errorConfig);
  } catch (error) {
    if (error instanceof ApiError) {
      handleApiErrorIntercept(error, config?.errorConfig);
    }
    throw error;
  }
}

// 基础 PUT 请求
async function basePut<T = unknown>(
  endpoint: string,
  data?: unknown,
  config?: RequestConfig
): Promise<ApiResponse<T>> {
  try {
    const response = await baseFetch(getFullUrl(endpoint), {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        ...config?.headers,
      },
      body: data ? JSON.stringify(data) : undefined,
      ...config,
    });

    return handleResponse<T>(response, config?.errorConfig);
  } catch (error) {
    if (error instanceof ApiError) {
      handleApiErrorIntercept(error, config?.errorConfig);
    }
    throw error;
  }
}

// 基础 DELETE 请求
async function baseDelete<T = unknown>(
  endpoint: string,
  config?: RequestConfig
): Promise<ApiResponse<T>> {
  try {
    const response = await baseFetch(getFullUrl(endpoint), {
      method: "DELETE",
      ...config,
    });

    return handleResponse<T>(response, config?.errorConfig);
  } catch (error) {
    if (error instanceof ApiError) {
      handleApiErrorIntercept(error, config?.errorConfig);
    }
    throw error;
  }
}

// 缓存配置接口
interface ApiCacheConfig {
  enabled?: boolean; // 是否启用缓存，默认 false（POST通常不缓存）
  ttl?: number; // 缓存时间（毫秒），默认使用 CACHE_CONFIG.defaultTTL
  key?: string; // 自定义缓存键
  strategy?: "memory" | "localStorage" | "sessionStorage"; // 缓存策略，默认 memory
  invalidatePatterns?: string[]; // 缓存失效模式
  conditions?: {
    // 缓存条件
    onSuccess?: boolean; // 仅成功时缓存，默认 true
    statusCodes?: number[]; // 特定状态码才缓存，默认 [200, 201]
  };
}

// 增强的请求配置
interface EnhancedRequestConfig extends Omit<RequestConfig, "cache"> {
  cache?: ApiCacheConfig;
}

// 预设缓存配置
export const CACHE_PRESETS = {
  // POST 请求通常不缓存，但某些场景可能需要
  POST_NO_CACHE: {
    enabled: false,
  } as ApiCacheConfig,

  // 短期缓存（适用于频繁的相同 POST 请求）
  POST_SHORT_CACHE: {
    enabled: true,
    ttl: 30 * 1000, // 30秒
    strategy: "memory" as const,
    conditions: { onSuccess: true, statusCodes: [200, 201] },
  } as ApiCacheConfig,

  // 上传结果缓存（避免重复上传相同文件）
  UPLOAD_RESULT_CACHE: {
    enabled: true,
    ttl: 10 * 60 * 1000, // 10分钟
    strategy: "sessionStorage" as const,
    conditions: { onSuccess: true, statusCodes: [200, 201] },
  } as ApiCacheConfig,

  // 中期缓存（适用于相对稳定的数据）
  MEDIUM_CACHE: {
    enabled: true,
    ttl: 5 * 60 * 1000, // 5分钟
    strategy: "memory" as const,
    conditions: { onSuccess: true },
  } as ApiCacheConfig,
} as const;

// 缓存项接口
interface CacheItem {
  data: unknown;
  timestamp: number;
  ttl: number;
}

// 缓存管理器 - 函数式实现
const memoryCache = new Map<string, CacheItem>();

// 简单的字符串哈希函数
function hashString(str: string): string {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash = hash & hash; // 转换为32位整数
  }
  return Math.abs(hash).toString(36);
}

// 生成缓存键
function generateCacheKey(
  url: string,
  options?: RequestInit,
  customKey?: string
): string {
  if (customKey) {
    return customKey;
  }
  const method = options?.method || "POST";
  const body = options?.body ? hashString(JSON.stringify(options.body)) : "";
  return `api_cache:${method}:${url}:${body}`;
}

// 检查是否应该缓存
function shouldCache(
  response: ApiResponse<unknown>,
  config?: ApiCacheConfig
): boolean {
  if (!config?.enabled) {
    return false;
  }

  const conditions = config.conditions || {
    onSuccess: true,
    statusCodes: [200, 201],
  };

  // 检查成功条件
  if (conditions.onSuccess && !response.success) {
    return false;
  }

  return true;
}

// 清理最旧的内存缓存项
function cleanupOldestMemoryItems(): void {
  const entries = Array.from(memoryCache.entries());
  entries.sort((a, b) => a[1].timestamp - b[1].timestamp);

  // 删除最旧的 20% 项目
  const deleteCount = Math.floor(entries.length * 0.2);
  for (let i = 0; i < deleteCount; i++) {
    memoryCache.delete(entries[i][0]);
  }
}

// 设置缓存
function setCacheItem(
  key: string,
  data: unknown,
  ttl: number,
  strategy: "memory" | "localStorage" | "sessionStorage" = "memory"
): void {
  const item: CacheItem = {
    data,
    timestamp: Date.now(),
    ttl,
  };

  // 内存缓存
  if (strategy === "memory") {
    // 检查内存缓存大小限制
    if (memoryCache.size >= 100) {
      // 使用固定值替代 CACHE_ENV.MAX_MEMORY_ITEMS
      cleanupOldestMemoryItems();
    }
    memoryCache.set(key, item);

    return;
  }

  // 持久化缓存
  if (typeof window !== "undefined") {
    try {
      const storage =
        strategy === "localStorage"
          ? window.localStorage
          : window.sessionStorage;
      storage.setItem(key, JSON.stringify(item));
    } catch (error) {
      console.warn(`Failed to set ${strategy} cache:`, error);
      // 降级到内存缓存
      memoryCache.set(key, item);
    }
  }
}

// 获取缓存
function getCacheItem(
  key: string,
  strategy: "memory" | "localStorage" | "sessionStorage" = "memory"
): unknown | null {
  let item: CacheItem | null = null;

  // 内存缓存
  if (strategy === "memory") {
    item = memoryCache.get(key) || null;
  } else if (typeof window !== "undefined") {
    // 持久化缓存
    try {
      const storage =
        strategy === "localStorage"
          ? window.localStorage
          : window.sessionStorage;
      const cached = storage.getItem(key);
      if (cached) {
        item = JSON.parse(cached);
      }
    } catch (error) {
      console.warn(`Failed to get ${strategy} cache:`, error);
    }
  }

  if (!item) {
    return null;
  }

  // 检查是否过期
  if (Date.now() - item.timestamp > item.ttl) {
    deleteCacheItem(key, strategy);
    return null;
  }

  return item.data;
}

// 删除缓存
function deleteCacheItem(
  key: string,
  strategy?: "memory" | "localStorage" | "sessionStorage"
): void {
  if (!strategy) {
    // 删除所有策略的缓存
    memoryCache.delete(key);
    if (typeof window !== "undefined") {
      try {
        window.localStorage.removeItem(key);
        window.sessionStorage.removeItem(key);
      } catch (error) {
        console.warn("Failed to remove storage cache:", error);
      }
    }
    return;
  }

  if (strategy === "memory") {
    memoryCache.delete(key);
  } else if (typeof window !== "undefined") {
    try {
      const storage =
        strategy === "localStorage"
          ? window.localStorage
          : window.sessionStorage;
      storage.removeItem(key);
    } catch (error) {
      console.warn(`Failed to remove ${strategy} cache:`, error);
    }
  }
}

// 清空所有缓存
function clearAllCache(): void {
  memoryCache.clear();
  if (typeof window !== "undefined") {
    try {
      // 只清除我们的缓存项
      const keysToRemove: string[] = [];
      for (let i = 0; i < window.localStorage.length; i++) {
        const key = window.localStorage.key(i);
        if (key?.startsWith("api_cache:")) {
          keysToRemove.push(key);
        }
      }
      keysToRemove.forEach((key) => window.localStorage.removeItem(key));

      // 清除 sessionStorage
      for (let i = 0; i < window.sessionStorage.length; i++) {
        const key = window.sessionStorage.key(i);
        if (key?.startsWith("api_cache:")) {
          keysToRemove.push(key);
        }
      }
      keysToRemove.forEach((key) => window.sessionStorage.removeItem(key));
    } catch (error) {
      console.warn("Failed to clear storage cache:", error);
    }
  }
}

// 批量失效缓存
function invalidateCacheByPattern(patterns: string[]): void {
  patterns.forEach((pattern) => {
    // 清除内存缓存
    for (const key of memoryCache.keys()) {
      if (key.includes(pattern)) {
        memoryCache.delete(key);
      }
    }

    // 清除持久化缓存
    if (typeof window !== "undefined") {
      try {
        [window.localStorage, window.sessionStorage].forEach((storage) => {
          const keysToRemove: string[] = [];
          for (let i = 0; i < storage.length; i++) {
            const key = storage.key(i);
            if (key?.includes(pattern)) {
              keysToRemove.push(key);
            }
          }
          keysToRemove.forEach((key) => storage.removeItem(key));
        });
      } catch (error) {
        console.warn("Failed to invalidate storage cache by pattern:", error);
      }
    }
  });
}

// 清理过期的内存缓存
function cleanupExpiredCache(): void {
  const now = Date.now();
  for (const [key, item] of memoryCache.entries()) {
    if (now - item.timestamp > item.ttl) {
      memoryCache.delete(key);
    }
  }
}

// 获取缓存统计信息
function getCacheStats(): {
  memorySize: number;
  memoryKeys: string[];
  storageSize: number;
  storageKeys: string[];
} {
  const memoryKeys = Array.from(memoryCache.keys());
  const storageKeys: string[] = [];

  if (typeof window !== "undefined") {
    try {
      [window.localStorage, window.sessionStorage].forEach((storage) => {
        for (let i = 0; i < storage.length; i++) {
          const key = storage.key(i);
          if (key?.startsWith("api_cache:")) {
            storageKeys.push(key);
          }
        }
      });
    } catch (error) {
      console.warn("Failed to get storage cache stats:", error);
    }
  }

  return {
    memorySize: memoryCache.size,
    memoryKeys,
    storageSize: storageKeys.length,
    storageKeys,
  };
}

// 定期清理缓存
if (typeof window !== "undefined") {
  setInterval(() => cleanupExpiredCache(), 60000); // 60秒清理间隔
}

// 增强的 POST 请求，支持缓存
async function postWithCache<T = unknown>(
  endpoint: string,
  data?: unknown,
  config?: EnhancedRequestConfig
): Promise<ApiResponse<T>> {
  const { cache, ...requestConfig } = config || {};

  // 如果启用缓存，先检查缓存
  if (cache?.enabled) {
    const cacheKey = generateCacheKey(
      endpoint,
      { method: "POST", body: data ? JSON.stringify(data) : undefined },
      cache.key
    );
    const strategy = cache.strategy || "memory";
    const cachedData = getCacheItem(cacheKey, strategy);

    if (cachedData) {
      return cachedData as ApiResponse<T>;
    }

    // 执行请求
    const response = await basePost<T>(endpoint, data, requestConfig);

    // 检查是否应该缓存响应
    if (shouldCache(response, cache)) {
      const ttl = cache.ttl || CACHE_CONFIG.defaultTTL;
      setCacheItem(cacheKey, response, ttl, strategy);
    }

    return response;
  }

  // 不使用缓存，直接执行请求
  return basePost<T>(endpoint, data, requestConfig);
}

// 增强的 upload 请求，支持缓存
async function uploadWithCache<T = unknown>(
  endpoint: string,
  file: File | FormData,
  config?: EnhancedRequestConfig
): Promise<ApiResponse<T>> {
  const { cache, ...requestConfig } = config || {};

  // 如果启用缓存，先检查缓存
  if (cache?.enabled) {
    // 为文件上传生成特殊的缓存键
    const fileInfo =
      file instanceof File
        ? `${file.name}_${file.size}_${file.lastModified}`
        : "formdata";
    const cacheKey = cache.key || `api_cache:UPLOAD:${endpoint}:${fileInfo}`;
    const strategy = cache.strategy || "sessionStorage";
    const cachedData = getCacheItem(cacheKey, strategy);

    if (cachedData) {
      return cachedData as ApiResponse<T>;
    }

    // 执行上传
    const response = await baseUpload<T>(endpoint, file, requestConfig);

    // 检查是否应该缓存响应
    if (shouldCache(response, cache)) {
      const ttl = cache.ttl || CACHE_CONFIG.defaultTTL;
      setCacheItem(cacheKey, response, ttl, strategy);
    }

    return response;
  }

  // 不使用缓存，直接执行上传
  return baseUpload<T>(endpoint, file, requestConfig);
}

// 统一的 POST 方法，支持缓存
async function post<T = unknown>(
  endpoint: string,
  data?: unknown,
  config?: RequestConfig | EnhancedRequestConfig
): Promise<ApiResponse<T>> {
  // 检查是否为 EnhancedRequestConfig 类型（包含 cache 属性）
  if (config && "cache" in config && config.cache) {
    return postWithCache<T>(endpoint, data, config as EnhancedRequestConfig);
  }
  // 移除 cache 属性以兼容基类（如果存在）
  const { cache: _cache, ...baseConfig } =
    (config as EnhancedRequestConfig) || {};
  return basePost<T>(endpoint, data, baseConfig as RequestConfig);
}

// 统一的 upload 方法，支持缓存
async function upload<T = unknown>(
  endpoint: string,
  file: File | FormData,
  config?:
    | Omit<RequestConfig, "headers">
    | (Omit<EnhancedRequestConfig, "cache"> & { cache?: ApiCacheConfig })
): Promise<ApiResponse<T>> {
  // 检查是否为带缓存配置的类型
  if (config && "cache" in config && config.cache) {
    return uploadWithCache<T>(endpoint, file, config as EnhancedRequestConfig);
  }
  // 移除 cache 属性以兼容基类（如果存在）
  const { cache: _cache, ...baseConfig } =
    (config as Omit<EnhancedRequestConfig, "cache"> & {
      cache?: ApiCacheConfig;
    }) || {};
  return baseUpload<T>(
    endpoint,
    file,
    baseConfig as Omit<RequestConfig, "headers">
  );
}

// 缓存管理方法
function invalidateCache(pattern?: string): void {
  if (pattern) {
    invalidateCacheByPattern([pattern]);
  } else {
    clearAllCache();
  }
}

// 批量失效缓存
function invalidateCacheByPatterns(patterns: string[]): void {
  invalidateCacheByPattern(patterns);
}

// 统一的 GET 方法
async function get<T = unknown>(
  endpoint: string,
  config?: RequestConfig
): Promise<ApiResponse<T>> {
  return baseGet<T>(endpoint, config);
}

// 统一的 PUT 方法
async function put<T = unknown>(
  endpoint: string,
  data?: unknown,
  config?: RequestConfig
): Promise<ApiResponse<T>> {
  return basePut<T>(endpoint, data, config);
}

// 统一的 DELETE 方法
async function del<T = unknown>(
  endpoint: string,
  config?: RequestConfig
): Promise<ApiResponse<T>> {
  return baseDelete<T>(endpoint, config);
}

// 便捷的 API 方法
export const api = {
  // 基础请求方法
  get,
  post,
  put,
  delete: del,
  upload,

  // 带缓存的请求方法
  postWithCache,
  uploadWithCache,

  // 缓存管理
  cache: {
    invalidate: invalidateCache,
    invalidateByPatterns: invalidateCacheByPatterns,
    stats: getCacheStats,
    cleanup: cleanupExpiredCache,
  },
};

// 错误处理工具
export function handleApiError(error: unknown): string {
  if (error instanceof ApiError) {
    return error.message;
  }

  if (error instanceof Error) {
    return error.message;
  }

  return "An unknown error occurred";
}

export default api;
