"use client";

import { useState, useCallback } from "react";
import { DataLoader } from "../core/DataLoader";

/**
 * 地图数据管理Hook
 * 管理托儿所数据和ZIP边界数据的加载状态
 */
export const useMapData = () => {
  const [dataLoader] = useState(() => new DataLoader());
  const [zipBoundariesData, setZipBoundariesData] = useState<any>(null);
  const [zipCoordinates, setZipCoordinates] = useState<Map<string, [number, number]>>(new Map());
  const [isZipDataLoaded, setIsZipDataLoaded] = useState(false);

  /**
   * 预加载ZIP边界数据
   */
  const preloadZipBoundaries = useCallback(async () => {
    if (isZipDataLoaded) {
      return zipBoundariesData;
    }

    try {
      const data = await dataLoader.preloadZipBoundaries();
      if (data) {
        setZipBoundariesData(data);
        setZipCoordinates(dataLoader.getZipCoordinates());
        setIsZipDataLoaded(true);
      }
      return data;
    } catch (error) {
      console.error("Error preloading ZIP boundaries:", error);
      return null;
    }
  }, [dataLoader, isZipDataLoaded, zipBoundariesData]);

  /**
   * 加载托儿所数据
   */
  const loadDaycareData = useCallback(async (zipData?: any) => {
    try {
      return await dataLoader.loadDaycareData(zipData);
    } catch (error) {
      console.error("Error loading daycare data:", error);
      throw error;
    }
  }, [dataLoader]);

  /**
   * 重置数据状态
   */
  const resetData = useCallback(() => {
    dataLoader.reset();
    setZipBoundariesData(null);
    setZipCoordinates(new Map());
    setIsZipDataLoaded(false);
  }, [dataLoader]);

  return {
    // 数据状态
    zipBoundariesData,
    zipCoordinates,
    isZipDataLoaded,
    
    // 数据操作方法
    preloadZipBoundaries,
    loadDaycareData,
    resetData,
    
    // DataLoader实例
    dataLoader,
  };
};
