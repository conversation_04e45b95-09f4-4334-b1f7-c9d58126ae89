import { ReactNode } from "react";
import { FormLabel } from "./form";

interface RequiredLabelProps {
  children: ReactNode;
  required?: boolean;
  className?: string;
}

/**
 * 带有必填标记的标签组件
 * 当 required 为 true 时，会在标签前显示红色的 * 号
 */
export const RequiredLabel = ({
  children,
  required = false,
  className,
}: RequiredLabelProps) => {
  return (
    <FormLabel className={className}>
      {required && (
        <span className="text-destructive translate-y-0.5 -mr-1">*</span>
      )}
      {children}
    </FormLabel>
  );
};

export default RequiredLabel;
