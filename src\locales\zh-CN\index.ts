// 定义需要导入的文件列表
const fileNames = ["home", "route", "message", "nav", "map"];

// 动态导入所有文件
const modules = await Promise.all(
  fileNames.map(async (fileName) => {
    try {
      const importedModule = await import(`./${fileName}.json`);
      return { [`./${fileName}.json`]: importedModule };
    } catch (error) {
      console.warn(`Failed to import ${fileName}.json:`, error);
      return null;
    }
  })
).then((results) => Object.assign({}, ...results.filter(Boolean)));

const zhCN: Record<string, any> = {};

Object.entries(modules).forEach(([path, importedModule]) => {
  const key = path.replace("./", "").replace(".json", "");
  zhCN[key] = (importedModule as any).default || importedModule;
});

export default zhCN;
