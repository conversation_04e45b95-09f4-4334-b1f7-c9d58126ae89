"use client";

import { HomeIcon } from "@heroicons/react/24/solid";
import * as React from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar";
import { NavMain } from "./NavMain";
import { NavUser } from "./NavUser";
import { TeamSwitcher } from "./Role";

const data = {
  user: {
    name: "cn",
    email: "<EMAIL>",
    avatar: "",
    phone: "1234567890",
    id: "1",
    username: "cn",
  },
  navMain: [
    {
      title: "route:home",
      url: "/",
      icon: HomeIcon,
    },
  ],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <TeamSwitcher />
      </SidebarHeader>
      <SidebarContent className="overflow-hidden">
        <ScrollArea className="flex-1 h-full">
          <div className="space-y-1 p-2">
            <NavMain items={data.navMain} />
          </div>
        </ScrollArea>
      </SidebarContent>
      <SidebarFooter className="!p-0">
        <NavUser user={data.user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
