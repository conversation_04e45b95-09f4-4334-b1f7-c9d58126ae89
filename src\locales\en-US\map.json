{"search": {"placeholder": "Search California addresses...", "searching": "Searching...", "noResults": "No addresses found", "searchError": "Search failed, please try again", "clearSearch": "Clear search", "selectLocation": "Select location"}, "loading": {"initializing": "Initializing map...", "loadingBoundaries": "Loading ZIP boundaries...", "calculatingCoordinates": "Calculating coordinates...", "loadingData": "Loading daycare data...", "complete": "Complete", "rendering": "Rendering...", "pleaseWait": "Loading map data, please wait..."}, "boundaries": {"show": "Show Boundaries", "hide": "Hide Boundaries", "loading": "Loading..."}, "popup": {"title": "Daycare Data Analysis", "zipCode": "ZIP", "totalCapacity": "Total Capacity", "totalBirths": "Total Births", "saturationLevel": "Saturation Level", "yearData": "Year Data", "saturation": {"low": "Low Saturation", "medium": "Medium Saturation", "high": "High Saturation", "veryHigh": "Very High Saturation"}}, "errors": {"mapLoadFailed": "Map loading failed", "dataLoadFailed": "Data loading failed", "searchFailed": "Search request failed", "zipDataNotAvailable": "ZIP boundaries data not available", "mapInitError": "Map initialization error"}, "common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "confirm": "Confirm", "close": "Close", "retry": "Retry"}, "page": {"title": "California Daycare Market Saturation Analysis", "dataYear": "Data Year:"}, "legend": {"title": "2023 Saturation Legend", "totalZipCodes": "Total ZIP Codes:", "averageSaturation": "Average Saturation:", "marketStatus": "Market Status:", "circleSizeExplanation": "Circle Size Explanation:", "lowSaturation": "Low Saturation", "mediumSaturation": "Medium Saturation", "highSaturation": "High Saturation", "veryHighSaturation": "Very High Saturation", "dataSource": "Data Source: 2023 California Daycare Market Research", "marketOverSaturated": "Market Over-saturated", "highCompetition": "High Competition", "moderateCompetition": "Moderate Competition", "growthOpportunity": "Growth Opportunity", "mostAreasCompetitive": "Most areas highly competitive", "majorityAreasCompetitive": "Majority areas competitive", "moderateCompetitionLevel": "Moderate competition level", "marketOpportunityExists": "Market opportunities exist"}, "map": {"loadingData": "Loading map data...", "loadError": "Map loading error"}}