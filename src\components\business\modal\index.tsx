import clsx from "clsx";
import { JSX } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";

export function DialogModal({
  title,
  description,
  open,
  children,
  openChange,
  onInteractOutside = false,
  className = "w-[500px]",
}: {
  className?: string;
  title: string | JSX.Element;
  description?: string | JSX.Element;
  open: boolean;
  children: React.ReactNode;
  openChange: (open: boolean) => void;
  onInteractOutside?: boolean;
}) {
  return (
    <Dialog open={open} onOpenChange={openChange}>
      <DialogContent
        className={clsx("border-border", className)}
        onInteractOutside={(e) => {
          if (!onInteractOutside) {
            e.preventDefault();
          }
        }}
      >
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          {description ? (
            <DialogDescription>{description}</DialogDescription>
          ) : null}
        </DialogHeader>
        <div className="flex items-center mt-4">
          <div className="grid flex-1">
            <ScrollArea className="max-h-[40rem]">{children}</ScrollArea>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
