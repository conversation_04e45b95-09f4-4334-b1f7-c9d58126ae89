"use client";

import clsx from "clsx";
import { ChevronRight, type LucideIcon } from "lucide-react";
import { usePathname, useRouter } from "next/navigation";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  SidebarGroup,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@/components/ui/sidebar";
import { useLanguage } from "@/hooks/useLanguage";
import { useAppStore } from "@/store/useAppStore";

// 路由匹配函数 - 解决主页路由匹配问题
const isActive = (pathname: string | null, itemUrl: string): boolean => {
  if (!pathname) {
    return false;
  }

  // 主页精确匹配
  if (itemUrl === "/") {
    return pathname === "/";
  }
  // 其他路由前缀匹配
  return pathname.startsWith(itemUrl);
};

// 类型定义
type MenuItem = {
  title: string;
  url: string;
  icon?: LucideIcon;
  isActive?: boolean;
  items?: {
    title: string;
    url: string;
  }[];
};

// 子菜单项组件
function NavSubMenuItem({
  subItem,
  pathname,
  onNavigate,
  onMouseEnter,
}: {
  subItem: { title: string; url: string };
  pathname: string | null;
  onNavigate: (url: string) => void;
  onMouseEnter: (url: string) => void;
}) {
  const { t } = useLanguage();

  return (
    <SidebarMenuSubItem
      onClick={() => {
        if (pathname === subItem.url) {
          return;
        }
        onNavigate(subItem.url);
      }}
      onMouseEnter={() => onMouseEnter(subItem.url)}
      className={clsx(
        "h-14 flex items-center pl-2 w-full",
        isActive(pathname, subItem.url)
          ? "bg-primary-alpha-10 text-primary active-nav"
          : "hover:bg-surface-light"
      )}
    >
      <SidebarMenuSubButton
        className={clsx(
          "pl-8 text-text-secondary h-full w-full cursor-pointer",
          isActive(pathname, subItem.url)
            ? "hover:bg-transparent hover:!text-primary !text-primary"
            : ""
        )}
      >
        <span>{t(subItem.title)}</span>
      </SidebarMenuSubButton>
    </SidebarMenuSubItem>
  );
}

// 普通菜单项组件
function NavMenuItem({
  item,
  pathname,
  onNavigate,
  onMouseEnter,
}: {
  item: MenuItem;
  pathname: string | null;
  onNavigate: (url: string) => void;
  onMouseEnter: (url: string) => void;
}) {
  const { t } = useLanguage();

  return (
    <SidebarMenuItem
      onClick={() => {
        if (pathname === item.url) {
          return;
        }
        onNavigate(item.url);
      }}
      onMouseEnter={() => onMouseEnter(item.url)}
      className={clsx(
        "h-14 flex items-center pl-2 cursor-pointer",
        isActive(pathname, item.url)
          ? "bg-primary-alpha-10 text-primary active-nav"
          : "hover:bg-surface-light text-text-secondary"
      )}
    >
      <SidebarMenuButton
        tooltip={item.title}
        className={clsx(
          "h-full",
          isActive(pathname, item.url)
            ? "hover:bg-transparent hover:!text-primary"
            : ""
        )}
      >
        {item.icon && <item.icon className="!w-5 !h-5" />}
        <span>{t(item.title)}</span>
      </SidebarMenuButton>
    </SidebarMenuItem>
  );
}

// 带子菜单的菜单项组件
function NavMenuItemWithSub({
  item,
  pathname,
  onNavigate,
  onMouseEnter,
}: {
  item: MenuItem;
  pathname: string | null;
  onNavigate: (url: string) => void;
  onMouseEnter: (url: string) => void;
}) {
  const { t } = useLanguage();

  return (
    <Collapsible asChild className="group/collapsible">
      <SidebarMenuItem className="text-text-secondary">
        <CollapsibleTrigger asChild>
          <SidebarMenuButton
            tooltip={item.title}
            className="!h-14 pl-4 whitespace-nowrap"
          >
            {item.icon && <item.icon className="!w-5 !h-5" />}
            <span>{t(item.title)}</span>
            <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
          </SidebarMenuButton>
        </CollapsibleTrigger>
        <CollapsibleContent>
          <SidebarMenuSub className="border-none  !mx-0 !px-0 !gap-0 !p-0">
            {item.items?.map((subItem) => (
              <NavSubMenuItem
                key={subItem.title}
                subItem={subItem}
                pathname={pathname}
                onNavigate={onNavigate}
                onMouseEnter={onMouseEnter}
              />
            ))}
          </SidebarMenuSub>
        </CollapsibleContent>
      </SidebarMenuItem>
    </Collapsible>
  );
}

export function NavMain({ items }: { items: MenuItem[] }) {
  const pathname = usePathname();
  const router = useRouter();
  const { setCurrentRoute } = useAppStore();

  const handleNavigate = (url: string) => {
    setCurrentRoute(url);
    router.push(url);
  };

  const handleMouseEnter = (url: string) => {
    // 预加载路由
    router.prefetch(url);
  };

  return (
    <SidebarGroup className="pl-0">
      <SidebarMenu>
        {items.map((item) => {
          // 判断是否有子菜单项
          const hasSubItems = item.items && item.items.length > 0;

          if (hasSubItems) {
            // 有子菜单项时使用 NavMenuItemWithSub 组件
            return (
              <NavMenuItemWithSub
                key={item.title}
                item={item}
                pathname={pathname}
                onNavigate={handleNavigate}
                onMouseEnter={handleMouseEnter}
              />
            );
          } else {
            // 没有子菜单项时使用 NavMenuItem 组件
            return (
              <NavMenuItem
                key={item.title}
                item={item}
                pathname={pathname}
                onNavigate={handleNavigate}
                onMouseEnter={handleMouseEnter}
              />
            );
          }
        })}
      </SidebarMenu>
    </SidebarGroup>
  );
}
