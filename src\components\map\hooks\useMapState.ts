"use client";

import { useState, useCallback } from "react";

/**
 * 地图状态管理Hook
 * 管理地图的各种UI状态
 */
export const useMapState = () => {
  const [isMapLoaded, setIsMapLoaded] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [loadingStatus, setLoadingStatus] = useState("");
  const [showZipBoundaries, setShowZipBoundaries] = useState(false);
  const [isShowingBoundaries, setIsShowingBoundaries] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  /**
   * 设置地图加载状态
   */
  const setMapLoaded = useCallback((loaded: boolean) => {
    setIsMapLoaded(loaded);
  }, []);

  /**
   * 设置加载状态
   */
  const setLoadingState = useCallback((loading: boolean, status?: string) => {
    setIsLoading(loading);
    if (status !== undefined) {
      setLoadingStatus(status);
    }
  }, []);

  /**
   * 更新加载状态文本
   */
  const updateLoadingStatus = useCallback((status: string) => {
    setLoadingStatus(status);
  }, []);

  /**
   * 切换ZIP边界显示状态
   */
  const toggleZipBoundaries = useCallback(() => {
    setShowZipBoundaries(prev => !prev);
  }, []);

  /**
   * 设置ZIP边界显示状态
   */
  const setZipBoundariesVisible = useCallback((visible: boolean) => {
    setShowZipBoundaries(visible);
  }, []);

  /**
   * 设置边界渲染状态
   */
  const setBoundariesRendering = useCallback((rendering: boolean) => {
    setIsShowingBoundaries(rendering);
  }, []);

  /**
   * 设置错误状态
   */
  const setErrorState = useCallback((error: Error | null) => {
    setError(error);
  }, []);

  /**
   * 清除错误状态
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  /**
   * 重置所有状态
   */
  const resetState = useCallback(() => {
    setIsMapLoaded(false);
    setIsLoading(true);
    setLoadingStatus("");
    setShowZipBoundaries(false);
    setIsShowingBoundaries(false);
    setError(null);
  }, []);

  return {
    // 状态值
    isMapLoaded,
    isLoading,
    loadingStatus,
    showZipBoundaries,
    isShowingBoundaries,
    error,

    // 状态更新方法
    setMapLoaded,
    setLoadingState,
    updateLoadingStatus,
    toggleZipBoundaries,
    setZipBoundariesVisible,
    setBoundariesRendering,
    setErrorState,
    clearError,
    resetState,
  };
};
