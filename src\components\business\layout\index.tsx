"use client";

import { memo } from "react";
import { <PERSON>roll<PERSON><PERSON> } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { Text, TextVariant } from "@/components/ui/Text";
import { useAppStore } from "@/store/useAppStore";
import { AppSidebar } from "./Sidebar";

// 缓存 Header 组件
const Header = memo(() => {
  const { headerSlot } = useAppStore();

  return (
    <header className="flex w-full sticky top-0 z-10 h-16 shrink-0 border-border border-b items-center gap-2 bg-surface transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12">
      <div className="flex items-center gap-2 px-4 w-full">
        <SidebarTrigger className="-ml-1 hover:bg-surface-light text-text-secondary hover:text-text-primary" />
        <Separator
          orientation="vertical"
          className="mr-2 data-[orientation=vertical]:h-4"
        />
        <Text
          variant={TextVariant.H5}
          className="font-bold flex justify-between items-center w-full"
        >
          {headerSlot && <>{headerSlot}</>}
        </Text>
      </div>
    </header>
  );
});

Header.displayName = "Header";

function Page({ children }: { children: React.ReactNode }) {
  const { sidebarCollapsed } = useAppStore();

  return (
    <SidebarProvider defaultOpen={!sidebarCollapsed}>
      <AppSidebar className="bg-surface border-border z-50" />
      <SidebarInset className="flex flex-col h-screen overflow-hidden">
        <Header />
        <ScrollArea className="flex-1 h-[calc(100vh-64px)]">
          <div className="flex flex-col gap-4 p-4">{children}</div>
        </ScrollArea>
      </SidebarInset>
    </SidebarProvider>
  );
}

export default memo(Page);
