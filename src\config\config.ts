/**
 * 统一配置文件
 * 整合所有配置到单一文件，简化项目结构
 */

// ==================== 环境配置 ====================
export const ENV = {
  NODE_ENV: process.env.NODE_ENV || "development",
  IS_DEVELOPMENT: process.env.NODE_ENV === "development",
  IS_PRODUCTION: process.env.NODE_ENV === "production",
  IS_TEST: process.env.NODE_ENV === "test",
} as const;

// ==================== API 配置 ====================
export const API_CONFIG = {
  baseURL: process.env.NEXT_PUBLIC_API_URL || "/api",
  version: process.env.NEXT_PUBLIC_API_VERSION || "v1",
  timeout: 10000,
  retryAttempts: 3,
  retryDelay: 1000,
} as const;

export const API_ENDPOINTS = {
  user: {
    profile: "/user/profile",
    settings: "/user/settings",
  },
  generate: {
    text: "/generate/text",
    image: "/generate/image",
  },
} as const;

// ==================== 缓存配置 ====================
export const CACHE_CONFIG = {
  defaultTTL: 5 * 60 * 1000, // 5分钟
  enabled: process.env.NEXT_PUBLIC_ENABLE_CACHE !== "false",
  debug: ENV.IS_DEVELOPMENT,
} as const;

// ==================== 主题配置 ====================
export const THEME_CONFIG = {
  defaultTheme: "dark" as "light" | "dark" | "system",
  enableTransitions: true,
  transitionDuration: 300,
  storageKey: "xhs-theme",
} as const;

// ==================== 应用配置 ====================
export const APP_CONFIG = {
  name: "XHS",
  version: "0.1.0",
  description: "AI-powered content generation platform",
  author: "XHS Team",
} as const;

export const ROUTES = {
  HOME: "/",
} as const;

export const STORAGE_KEYS = {
  USER_TOKEN: "user-token",
  USER_PREFERENCES: "user-preferences",
  THEME: "app-theme",
  LANGUAGE: "app-language",
} as const;

export const BREAKPOINTS = {
  xs: 480,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  "2xl": 1536,
} as const;

// ==================== 语言配置 ====================
export const LANGUAGE_CONFIG = {
  FALLBACK_INITIAL_LANGUAGE: "zh-CN",
  SUPPORTED_LANGUAGES: ["zh-CN", "en-US", "ja-JP"] as const,
  STORAGE_KEY: "xhs-language",
} as const;

// ==================== 功能开关 ====================
export const FEATURE_FLAGS = {
  ENABLE_DEBUG: ENV.IS_DEVELOPMENT,
  ENABLE_ANALYTICS: ENV.IS_PRODUCTION,
  ENABLE_ERROR_REPORTING: ENV.IS_PRODUCTION,
  LOG_LEVEL: process.env.NEXT_PUBLIC_LOG_LEVEL || "info",
} as const;

// ==================== 验证规则 ====================
export const VALIDATION_RULES = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  phone: /^[\+]?[1-9][\d]{0,15}$/,
  url: /^https?:\/\/.+/,
  password: {
    minLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: false,
  },
} as const;

// ==================== 错误消息 ====================
export const ERROR_MESSAGES = {
  NETWORK_ERROR: "网络连接错误，请检查网络设置",
  SERVER_ERROR: "服务器错误，请稍后重试",
  UNAUTHORIZED: "未授权访问，请先登录",
  NOT_FOUND: "请求的资源不存在",
  VALIDATION_ERROR: "输入数据验证失败",
  TIMEOUT_ERROR: "请求超时，请稍后重试",
} as const;

// ==================== 成功消息 ====================
export const SUCCESS_MESSAGES = {
  SAVE_SUCCESS: "保存成功",
  UPDATE_SUCCESS: "更新成功",
  DELETE_SUCCESS: "删除成功",
  UPLOAD_SUCCESS: "上传成功",
} as const;

// ==================== 统一配置对象 ====================
export const config = {
  env: ENV,
  api: API_CONFIG,
  cache: CACHE_CONFIG,
  theme: THEME_CONFIG,
  app: APP_CONFIG,
  language: LANGUAGE_CONFIG,
  features: FEATURE_FLAGS,
  routes: ROUTES,
  storage: STORAGE_KEYS,
  breakpoints: BREAKPOINTS,
  validation: VALIDATION_RULES,
  messages: {
    error: ERROR_MESSAGES,
    success: SUCCESS_MESSAGES,
  },
  endpoints: API_ENDPOINTS,
} as const;

// ==================== 便捷访问函数 ====================
export const isDev = () => ENV.IS_DEVELOPMENT;
export const isProd = () => ENV.IS_PRODUCTION;
export const isFeatureEnabled = (feature: keyof typeof FEATURE_FLAGS) =>
  Boolean(FEATURE_FLAGS[feature]);

// ==================== 配置验证 ====================
export function validateConfig() {
  const errors: string[] = [];

  if (!API_CONFIG.baseURL) {
    errors.push("API base URL is not configured");
  }

  if (!THEME_CONFIG.defaultTheme) {
    errors.push("Default theme is not configured");
  }

  if (errors.length > 0) {
    throw new Error(`Configuration validation failed:\n${errors.join("\n")}`);
  }

  return true;
}

// 开发环境下验证配置
if (ENV.IS_DEVELOPMENT) {
  try {
    validateConfig();
    // eslint-disable-next-line no-console
    console.log("✅ Configuration validation passed");
  } catch (error) {
    console.error("❌ Configuration validation failed:", error);
  }
}

// 默认导出
export default config;
