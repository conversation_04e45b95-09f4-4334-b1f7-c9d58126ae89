"use client";

import { Chev<PERSON>UpDown, Sun, Moon, Languages } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuGroup,
} from "@/components/ui/dropdown-menu";
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";
import { useLanguage } from "@/hooks/useLanguage";
import { useTheme } from "@/hooks/useTheme";
import { SUPPORTED_LANGUAGES } from "@/lib/i18n";

// 用户类型定义
interface IUser {
  username: string;
  email?: string;
  phone?: string;
  avatar?: string;
}

export function NavUser({ user }: { user: IUser }) {
  const { isMobile } = useSidebar();
  const { t, currentLanguage, changeLanguage } = useLanguage();
  const { theme, toggleTheme } = useTheme();

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <Avatar className="h-8 w-8 rounded-lg">
                <AvatarImage src={user?.avatar} alt={user.username} />
                <AvatarFallback className="rounded-lg">CN</AvatarFallback>
              </Avatar>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-medium">{user.username}</span>
                <span className="truncate text-xs">
                  {user.email || user.phone}
                </span>
              </div>
              <ChevronsUpDown className="ml-auto size-4" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg border-border bg-surface-light"
            side={isMobile ? "bottom" : "right"}
            align="end"
            sideOffset={4}
          >
            <DropdownMenuLabel className="p-0 font-normal">
              <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                <Avatar className="h-8 w-8 rounded-lg">
                  <AvatarImage src={user?.avatar} alt={user.username} />
                  <AvatarFallback className="rounded-lg">CN</AvatarFallback>
                </Avatar>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-medium">{user.username}</span>
                  <span className="truncate text-xs">
                    {user.email || user.phone}
                  </span>
                </div>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              {/* 主题切换 */}
              <DropdownMenuItem
                className="hover:bg-white-alpha-20 focus:bg-white-alpha-20 cursor-pointer"
                onClick={toggleTheme}
              >
                {theme === "light" ? <Moon /> : <Sun />}
                {t("nav:theme")} (
                {theme === "light" ? t("nav:lightTheme") : t("nav:darkTheme")})
              </DropdownMenuItem>

              {/* 语言切换 */}
              <DropdownMenuItem
                className="hover:bg-white-alpha-20 focus:bg-white-alpha-20 cursor-pointer"
                onClick={() => {
                  const newLanguage =
                    currentLanguage === SUPPORTED_LANGUAGES["zh-CN"]
                      ? SUPPORTED_LANGUAGES["en-US"]
                      : SUPPORTED_LANGUAGES["zh-CN"];
                  changeLanguage(newLanguage);
                }}
              >
                <Languages />
                {t("nav:language")} (
                {currentLanguage === SUPPORTED_LANGUAGES["zh-CN"]
                  ? t("nav:chinese")
                  : t("nav:english")}
                )
              </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            {/* <DropdownMenuItem
              className="hover:bg-white-alpha-20 focus:bg-white-alpha-20 cursor-pointer"
              onClick={() => {
                localStorage.clear();
                window.location.href = "/login";
              }}
            >
              <LogOut />
              {t("nav:logout")}
            </DropdownMenuItem> */}
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
