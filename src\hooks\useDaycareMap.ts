"use client";

import { useState, useEffect, useCallback } from "react";
import { LocalDataLoader } from "@/components/map/data/local-data-manager";
import type {
  MapState,
  DaycareData,
  DaycareStats,
  SearchResult,
  ApiResponse,
} from "@/types/daycare";

/**
 * 托儿所地图数据管理Hook
 */
export const useDaycareMap = () => {
  // 本地数据加载器实例
  const [localDataLoader] = useState(() => new LocalDataLoader());

  // 地图状态
  const [mapState, setMapState] = useState<MapState>({
    isLoaded: false,
    isLoading: false,
    error: null,
    data: null,
    stats: null,
    selectedZip: null,
    searchQuery: "",
    searchResults: [],
  });

  // 搜索相关状态
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);

  // 加载数据（使用本地数据管理器）
  const loadData = useCallback(async () => {
    console.log("🔄 开始从本地加载数据...");
    setMapState((prev) => ({ ...prev, isLoading: true, error: null }));

    try {
      // 使用本地数据管理器并行加载数据和统计信息
      const [pointDataResult, statsData] = await Promise.all([
        localDataLoader.loadPointData(),
        localDataLoader.getStats(),
      ]);

      console.log("✅ 本地数据加载成功:", {
        dataType: pointDataResult.type,
        dataCount: pointDataResult.data?.features?.length || 0,
        statsLoaded: !!statsData,
      });

      // 转换GeoJSON数据为DaycareData数组格式
      const daycareData: DaycareData[] = pointDataResult.data.features.map(
        (feature) => ({
          id:
            feature.properties.id ||
            `daycare-${Math.random().toString(36).substr(2, 9)}`,
          name: feature.properties.name || "未知托儿所",
          address: feature.properties.address || "",
          zip_code: feature.properties.zip_code || "",
          latitude: feature.geometry.coordinates[1],
          longitude: feature.geometry.coordinates[0],
          capacity: feature.properties.capacity || 0,
          enrollment: feature.properties.enrollment || 0,
          saturation: feature.properties.saturation || 0,
          saturation_level: feature.properties.saturation_level || "low",
          phone: feature.properties.phone || "",
          website: feature.properties.website || "",
          hours: feature.properties.hours || "",
          age_range: feature.properties.age_range || "",
          license_number: feature.properties.license_number || "",
          last_updated:
            feature.properties.last_updated || new Date().toISOString(),
        })
      );

      // 更新状态
      setMapState((prev) => ({
        ...prev,
        isLoading: false,
        data: daycareData,
        stats: statsData,
        error: null,
      }));
    } catch (error) {
      console.error("❌ 本地数据加载失败:", error);
      const errorMessage =
        error instanceof Error ? error.message : "本地数据加载失败";

      setMapState((prev) => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
    }
  }, [localDataLoader]);

  // 搜索地址
  const searchAddress = useCallback(
    async (query: string): Promise<SearchResult[]> => {
      const accessToken = process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN;

      if (!accessToken) {
        throw new Error("Mapbox access token is required");
      }

      const response = await fetch(
        `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(
          query
        )}.json?` +
          `access_token=${accessToken}&` +
          `country=US&` +
          `bbox=-124.4,32.5,-114.1,42.0&` + // 限制在加州范围
          `limit=5`
      );

      if (!response.ok) {
        throw new Error("搜索请求失败");
      }

      const data = await response.json();

      return data.features.map((feature: any) => ({
        id: feature.id,
        place_name: feature.place_name,
        center: feature.center,
        bbox: feature.bbox,
        context: feature.context,
      }));
    },
    []
  );

  // 处理搜索
  const handleSearch = useCallback(
    async (query: string) => {
      setSearchQuery(query);

      if (!query.trim()) {
        setSearchResults([]);
        return;
      }

      try {
        const results = await searchAddress(query);
        setSearchResults(results);

        // 更新地图状态
        setMapState((prev) => ({
          ...prev,
          searchQuery: query,
          searchResults: results,
        }));
      } catch (error) {
        console.error("Search error:", error);
        setSearchResults([]);
      }
    },
    [searchAddress]
  );

  // 处理搜索结果选择
  const handleSearchResultSelect = useCallback((result: SearchResult) => {
    setSearchQuery(result.place_name);
    setSearchResults([]);

    // 更新地图状态
    setMapState((prev) => ({
      ...prev,
      searchQuery: result.place_name,
      searchResults: [],
    }));

    // 这里可以触发地图飞行到选中位置
    // 通过事件或回调通知地图组件
    window.dispatchEvent(
      new CustomEvent("mapFlyTo", {
        detail: { coordinates: result.center, zoom: 12 },
      })
    );
  }, []);

  // 处理地图加载完成
  const handleMapLoad = useCallback(() => {
    setMapState((prev) => ({ ...prev, isLoaded: true }));
  }, []);

  // 处理地图错误
  const handleMapError = useCallback((error: Error) => {
    setMapState((prev) => ({
      ...prev,
      error: error.message,
      isLoading: false,
    }));
  }, []);

  // 选择ZIP码
  const selectZip = useCallback((zipCode: string | null) => {
    setMapState((prev) => ({ ...prev, selectedZip: zipCode }));
  }, []);

  // 获取特定ZIP码数据（使用本地数据管理器）
  const getZipData = useCallback(
    async (zipCode: string): Promise<DaycareData | null> => {
      try {
        const zipProperties = await localDataLoader.getZipData(zipCode);

        if (zipProperties) {
          // 转换为DaycareData格式
          return {
            id: zipCode,
            name: `ZIP ${zipCode} 区域`,
            address: "",
            zip_code: zipCode,
            latitude: 0, // 这里可以从ZIP坐标获取
            longitude: 0,
            capacity: zipProperties.total_capacity || 0,
            enrollment: zipProperties.total_enrollment || 0,
            saturation: zipProperties.saturation || 0,
            saturation_level: zipProperties.saturation_level || "low",
            phone: "",
            website: "",
            hours: "",
            age_range: "",
            license_number: "",
            last_updated: new Date().toISOString(),
            total_births: 0,
            total_capacity: zipProperties.total_capacity || 0,
          };
        } else {
          console.warn("⚠️ ZIP码数据未找到:", zipCode);
          return null;
        }
      } catch (error) {
        console.error("❌ ZIP码数据获取失败:", error);
        return null;
      }
    },
    [localDataLoader]
  );

  // 重置状态
  const resetState = useCallback(() => {
    setMapState({
      isLoaded: false,
      isLoading: false,
      error: null,
      data: null,
      stats: null,
      selectedZip: null,
      searchQuery: "",
      searchResults: [],
    });
    setSearchQuery("");
    setSearchResults([]);
  }, []);

  // 重新加载数据
  const reloadData = useCallback(() => {
    resetState();
    loadData();
  }, [resetState, loadData]);

  // 初始化时加载数据
  useEffect(() => {
    loadData();
  }, [loadData]);

  // 返回状态和方法
  return {
    // 状态
    mapState,
    searchQuery,
    searchResults,

    // 数据操作
    loadData,
    reloadData,
    getZipData,

    // 搜索操作
    handleSearch,
    handleSearchResultSelect,

    // 地图操作
    handleMapLoad,
    handleMapError,
    selectZip,

    // 工具方法
    resetState,

    // 计算属性
    isDataReady: mapState.data !== null && mapState.stats !== null,
    hasError: mapState.error !== null,
    isLoading: mapState.isLoading,
  };
};
