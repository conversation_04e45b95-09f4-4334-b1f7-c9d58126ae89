const fs = require('fs');
const https = require('https');
const path = require('path');

// 创建数据文件夹
const dataDir = path.join(__dirname, '..', 'public', 'data');
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

// 下载加州ZIP code数据
const downloadFile = (url, filename) => {
  return new Promise((resolve, reject) => {
    const filePath = path.join(dataDir, filename);
    const file = fs.createWriteStream(filePath);
    
    console.log(`正在下载: ${filename}`);
    
    https.get(url, (response) => {
      if (response.statusCode === 200) {
        response.pipe(file);
        file.on('finish', () => {
          file.close();
          console.log(`下载完成: ${filename}`);
          resolve(filePath);
        });
      } else {
        reject(new Error(`下载失败: ${response.statusCode}`));
      }
    }).on('error', (err) => {
      reject(err);
    });
  });
};

// 数据源列表
const dataSources = [
  {
    url: 'https://raw.githubusercontent.com/OpenDataDE/State-zip-code-GeoJSON/master/ca_california_zip_codes_geo.min.json',
    filename: 'california-zip-codes.geojson'
  }
];

// 下载所有数据文件
async function downloadAllData() {
  try {
    for (const source of dataSources) {
      await downloadFile(source.url, source.filename);
    }
    console.log('所有数据文件下载完成！');
  } catch (error) {
    console.error('下载失败:', error);
  }
}

downloadAllData();
