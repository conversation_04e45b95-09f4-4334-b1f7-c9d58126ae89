/**
 * 主题调试工具
 * 用于检查和调试地图组件的主题应用情况
 */

/**
 * 检查当前主题状态
 */
export const debugThemeStatus = () => {
  if (typeof window === "undefined") {
    console.log("🔍 Theme Debug: 运行在服务器端，无法检查主题");
    return;
  }

  const html = document.documentElement;
  const currentTheme = html.getAttribute("data-theme");
  
  console.group("🎨 地图主题调试信息");
  console.log("📋 当前主题:", currentTheme || "未设置");
  console.log("🏷️ HTML 元素:", html);
  
  // 检查 CSS 变量
  const computedStyle = getComputedStyle(html);
  const cssVars = {
    "--primary": computedStyle.getPropertyValue("--primary").trim(),
    "--primary-foreground": computedStyle.getPropertyValue("--primary-foreground").trim(),
    "--surface": computedStyle.getPropertyValue("--surface").trim(),
    "--surface-light": computedStyle.getPropertyValue("--surface-light").trim(),
    "--border": computedStyle.getPropertyValue("--border").trim(),
    "--text-primary": computedStyle.getPropertyValue("--text-primary").trim(),
    "--text-secondary": computedStyle.getPropertyValue("--text-secondary").trim(),
    "--radius": computedStyle.getPropertyValue("--radius").trim(),
    "--shadow-lg": computedStyle.getPropertyValue("--shadow-lg").trim(),
  };
  
  console.log("🎨 CSS 变量值:", cssVars);
  
  // 检查弹窗元素
  const popups = document.querySelectorAll(".mapboxgl-popup");
  const popupContents = document.querySelectorAll(".mapboxgl-popup-content");
  
  console.log("🗨️ 弹窗元素数量:", popups.length);
  console.log("📦 弹窗内容元素数量:", popupContents.length);
  
  if (popupContents.length > 0) {
    const firstPopup = popupContents[0] as HTMLElement;
    const popupStyle = getComputedStyle(firstPopup);
    
    console.log("🎯 第一个弹窗的计算样式:");
    console.log("  - background:", popupStyle.backgroundColor);
    console.log("  - color:", popupStyle.color);
    console.log("  - border:", popupStyle.border);
    console.log("  - border-radius:", popupStyle.borderRadius);
    console.log("  - box-shadow:", popupStyle.boxShadow);
  }
  
  // 检查主题相关的 CSS 规则
  const styleSheets = Array.from(document.styleSheets);
  let themeRulesCount = 0;
  
  try {
    styleSheets.forEach((sheet) => {
      if (sheet.cssRules) {
        Array.from(sheet.cssRules).forEach((rule) => {
          if (rule instanceof CSSStyleRule) {
            if (rule.selectorText?.includes('[data-theme=') || 
                rule.selectorText?.includes('html[data-theme=')) {
              themeRulesCount++;
            }
          }
        });
      }
    });
  } catch (e) {
    console.log("⚠️ 无法访问某些样式表（可能是跨域限制）");
  }
  
  console.log("📜 主题相关 CSS 规则数量:", themeRulesCount);
  
  console.groupEnd();
};

/**
 * 强制应用主题样式到弹窗
 */
export const forceApplyThemeToPopups = () => {
  if (typeof window === "undefined") return;
  
  const currentTheme = document.documentElement.getAttribute("data-theme") as "light" | "dark" || "dark";
  const popupContents = document.querySelectorAll(".mapboxgl-popup-content");
  
  console.log(`🔧 强制应用 ${currentTheme} 主题到 ${popupContents.length} 个弹窗`);
  
  const themeStyles = {
    light: {
      backgroundColor: "#f8f9fa",
      color: "#111827",
      border: "1px solid #e5e7eb",
      borderRadius: "0.625rem",
      boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1)",
    },
    dark: {
      backgroundColor: "#111111",
      color: "#ffffff",
      border: "1px solid #2a2a2a",
      borderRadius: "0.625rem",
      boxShadow: "0 10px 40px rgba(0, 0, 0, 0.6)",
    },
  };
  
  const styles = themeStyles[currentTheme];
  
  popupContents.forEach((popup) => {
    const element = popup as HTMLElement;
    Object.assign(element.style, styles);
  });
  
  // 也应用到弹窗内部元素
  const popupHeaders = document.querySelectorAll(".popup-header");
  popupHeaders.forEach((header) => {
    const element = header as HTMLElement;
    element.style.background = currentTheme === "light" 
      ? "linear-gradient(135deg, #ff2e4d 0%, #1d4ed8 100%)"
      : "linear-gradient(135deg, #ff2e4d 0%, #1e40af 100%)";
    element.style.color = "#ffffff";
  });
  
  const popupContentDivs = document.querySelectorAll(".popup-content");
  popupContentDivs.forEach((content) => {
    const element = content as HTMLElement;
    element.style.backgroundColor = styles.backgroundColor;
    element.style.color = styles.color;
  });
};

/**
 * 监听主题变化并自动应用
 */
export const setupThemeDebugger = () => {
  if (typeof window === "undefined") return;
  
  console.log("🔧 设置主题调试器");
  
  // 初始检查
  debugThemeStatus();
  
  // 监听主题变化
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (
        mutation.type === "attributes" &&
        mutation.attributeName === "data-theme"
      ) {
        console.log("🔄 检测到主题变化");
        setTimeout(() => {
          debugThemeStatus();
          forceApplyThemeToPopups();
        }, 100);
      }
    });
  });

  observer.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ["data-theme"],
  });
  
  // 监听弹窗创建
  const popupObserver = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      mutation.addedNodes.forEach((node) => {
        if (node instanceof Element) {
          if (node.classList.contains("mapboxgl-popup") || 
              node.querySelector(".mapboxgl-popup")) {
            console.log("🗨️ 检测到新弹窗创建");
            setTimeout(() => {
              forceApplyThemeToPopups();
            }, 50);
          }
        }
      });
    });
  });
  
  popupObserver.observe(document.body, {
    childList: true,
    subtree: true,
  });
  
  // 返回清理函数
  return () => {
    observer.disconnect();
    popupObserver.disconnect();
  };
};

/**
 * 手动触发主题应用（用于测试）
 */
export const manualApplyTheme = () => {
  console.log("🔧 手动触发主题应用");
  debugThemeStatus();
  forceApplyThemeToPopups();
};

// 在开发环境下自动设置调试器
if (typeof window !== "undefined" && process.env.NODE_ENV === "development") {
  // 延迟设置，确保 DOM 已加载
  setTimeout(() => {
    setupThemeDebugger();
    
    // 添加全局调试函数
    (window as any).debugMapTheme = {
      status: debugThemeStatus,
      apply: manualApplyTheme,
      force: forceApplyThemeToPopups,
    };
    
    console.log("🎯 地图主题调试工具已加载，使用 window.debugMapTheme 访问");
  }, 1000);
}
