/**
 * 地图主题工具类
 * 提供主题相关的工具函数和配置
 */

/**
 * 获取当前主题
 */
export const getCurrentTheme = (): "light" | "dark" => {
  if (typeof window === "undefined") return "dark";
  
  const theme = document.documentElement.getAttribute("data-theme");
  return (theme as "light" | "dark") || "dark";
};

/**
 * 监听主题变化
 */
export const observeThemeChanges = (callback: (theme: "light" | "dark") => void): (() => void) => {
  if (typeof window === "undefined") return () => {};

  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (
        mutation.type === "attributes" &&
        mutation.attributeName === "data-theme"
      ) {
        const newTheme = getCurrentTheme();
        callback(newTheme);
      }
    });
  });

  observer.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ["data-theme"],
  });

  // 返回清理函数
  return () => observer.disconnect();
};

/**
 * 获取主题相关的CSS变量值
 */
export const getThemeVariable = (variableName: string): string => {
  if (typeof window === "undefined") return "";
  
  return getComputedStyle(document.documentElement)
    .getPropertyValue(variableName)
    .trim();
};

/**
 * 获取主题相关的颜色配置
 */
export const getThemeColors = () => {
  return {
    surface: getThemeVariable("--surface"),
    surfaceLight: getThemeVariable("--surface-light"),
    border: getThemeVariable("--border"),
    textPrimary: getThemeVariable("--text-primary"),
    textSecondary: getThemeVariable("--text-secondary"),
    primary: getThemeVariable("--primary"),
    primaryForeground: getThemeVariable("--primary-foreground"),
    destructive: getThemeVariable("--destructive"),
    warning: getThemeVariable("--warning"),
    success: getThemeVariable("--success"),
  };
};

/**
 * 应用主题到地图控件
 */
export const applyThemeToMapControls = (theme: "light" | "dark") => {
  if (typeof window === "undefined") return;

  const colors = getThemeColors();
  
  // 更新导航控件样式
  const navControls = document.querySelectorAll(".mapboxgl-ctrl-group");
  navControls.forEach((control) => {
    const element = control as HTMLElement;
    element.style.backgroundColor = colors.surface;
    element.style.borderColor = colors.border;
    element.style.color = colors.textPrimary;
  });

  // 更新比例尺样式
  const scaleControls = document.querySelectorAll(".mapboxgl-ctrl-scale");
  scaleControls.forEach((control) => {
    const element = control as HTMLElement;
    element.style.backgroundColor = colors.surface;
    element.style.borderColor = colors.border;
    element.style.color = colors.textPrimary;
  });

  // 更新弹窗样式
  const popups = document.querySelectorAll(".mapboxgl-popup-content");
  popups.forEach((popup) => {
    const element = popup as HTMLElement;
    element.style.backgroundColor = colors.surface;
    element.style.borderColor = colors.border;
    element.style.color = colors.textPrimary;
  });
};

/**
 * 获取主题感知的地图样式URL
 */
export const getMapStyleUrl = (theme: "light" | "dark"): string => {
  const styles = {
    light: "mapbox://styles/mapbox/light-v11",
    dark: "mapbox://styles/mapbox/dark-v11",
  };
  
  return styles[theme];
};

/**
 * 创建主题感知的图层样式
 */
export const createThemeAwareLayerStyle = (
  baseStyle: any,
  themeOverrides: {
    light?: Partial<any>;
    dark?: Partial<any>;
  }
) => {
  const currentTheme = getCurrentTheme();
  const overrides = themeOverrides[currentTheme] || {};
  
  return {
    ...baseStyle,
    ...overrides,
  };
};

/**
 * 主题感知的颜色插值函数
 */
export const createThemeColorExpression = (
  property: string,
  colorMap: Record<string, { light: string; dark: string }>
) => {
  const currentTheme = getCurrentTheme();
  
  return [
    "match",
    ["get", property],
    ...Object.entries(colorMap).flatMap(([key, colors]) => [
      key,
      colors[currentTheme],
    ]),
    colorMap.default?.[currentTheme] || "#6b7280", // 默认颜色
  ];
};

/**
 * 检查是否支持深色模式
 */
export const supportsDarkMode = (): boolean => {
  if (typeof window === "undefined") return false;
  
  return window.matchMedia && window.matchMedia("(prefers-color-scheme: dark)").matches;
};

/**
 * 检查是否支持高对比度模式
 */
export const supportsHighContrast = (): boolean => {
  if (typeof window === "undefined") return false;
  
  return window.matchMedia && window.matchMedia("(prefers-contrast: high)").matches;
};

/**
 * 检查是否启用了减少动画模式
 */
export const prefersReducedMotion = (): boolean => {
  if (typeof window === "undefined") return false;
  
  return window.matchMedia && window.matchMedia("(prefers-reduced-motion: reduce)").matches;
};

/**
 * 获取响应式断点
 */
export const getBreakpoint = (): "mobile" | "tablet" | "desktop" => {
  if (typeof window === "undefined") return "desktop";
  
  const width = window.innerWidth;
  
  if (width < 768) return "mobile";
  if (width < 1024) return "tablet";
  return "desktop";
};

/**
 * 主题感知的动画配置
 */
export const getAnimationConfig = () => {
  const reducedMotion = prefersReducedMotion();
  
  return {
    duration: reducedMotion ? 0 : 300,
    easing: "ease-in-out",
    enabled: !reducedMotion,
  };
};

/**
 * 导出所有主题工具
 */
export const themeUtils = {
  getCurrentTheme,
  observeThemeChanges,
  getThemeVariable,
  getThemeColors,
  applyThemeToMapControls,
  getMapStyleUrl,
  createThemeAwareLayerStyle,
  createThemeColorExpression,
  supportsDarkMode,
  supportsHighContrast,
  prefersReducedMotion,
  getBreakpoint,
  getAnimationConfig,
};

export default themeUtils;
